# Real-Time On-Chain Anomaly Detection for Anti-Money Laundering

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Docker](https://img.shields.io/badge/docker-%230db7ed.svg?style=flat&logo=docker&logoColor=white)](https://www.docker.com/)
[![Kafka](https://img.shields.io/badge/Apache%20Kafka-000?style=flat&logo=apachekafka)](https://kafka.apache.org/)
[![Spark](https://img.shields.io/badge/Apache%20Spark-FDEE21?style=flat&logo=apachespark&logoColor=black)](https://spark.apache.org/)
[![Flink](https://img.shields.io/badge/Apache%20Flink-E6526F?style=flat&logo=Apache%20Flink&logoColor=white)](https://flink.apache.org/)
[![Neo4j](https://img.shields.io/badge/Neo4j-008CC1?style=flat&logo=neo4j&logoColor=white)](https://neo4j.com/)

## 🎯 Overview

A cutting-edge **real-time streaming pipeline** for blockchain Anti-Money Laundering (AML) that combines **Complex Event Processing (CEP)** with **Spatio-Temporal Graph Neural Networks** and **persistent graph storage**. This system enables **sub-second detection** of suspicious transaction patterns in blockchain networks, specifically targeting Ethereum transactions with **<1 second end-to-end inference** and **>90% explainability**.

### 🏆 Key Innovation
**First end-to-end hybrid system** that combines CEP's sub-second filtering with spatio-temporal GNN inference on sliding-window graphs, while maintaining persistent graph storage in Neo4j for investigative analysis and compliance auditing.

## 🔍 Problem Statement

Current blockchain AML solutions face four critical limitations:

1. **🌊 High Velocity Data Streams**: No existing framework supports real-time graph construction at scale for high-velocity blockchain streams
2. **🔧 Rigid CEP Rules**: Hand-crafted rules lack adaptability to evolving laundering topologies, leading to false negatives
3. **⏱️ Batch-Only GNNs**: Spatio-temporal GNNs confined to batch processing, preventing timely alerts on dynamic transaction networks
4. **🔍 Missing Explainability**: Absence of integrated, streaming-capable explainability inhibits investigator trust and compliance adoption
5. **📊 Graph Persistence Gap**: Lack of persistent graph storage prevents historical analysis and investigative queries

## 🎯 Research Objectives

### **Primary Aim**
Develop a hybrid real-time on-chain anomaly detection streaming pipeline employing Kafka-based event ingestion, Complex Event Processing pre-filtering, sliding-window graph construction in Spark with Neo4j persistence, and cloud-based TGAT inference with explainability.

### **Specific Objectives**

| Objective | Target Metric | Implementation |
|-----------|---------------|----------------|
| **CEP Filtering** | Prune >85% benign transactions | Kafka-Flink-SQL module for Sepolia events |
| **Graph Construction** | <200ms latency per snapshot | Spark Structured Streaming with 1-min sliding windows |
| **Graph Storage** | <100ms Neo4j ingestion | Neo4j Spark Connector for persistent storage |
| **TGAT Inference** | <1 second end-to-end | Google Colab GPU deployment with API serving |
| **Explainability** | >90% alert interpretability | GNNExplainer integration for node/edge attribution |
| **Performance** | >1000 tx/s throughput | Benchmark on Sepolia testnet |
| **Graph Queries** | <500ms investigative queries | Neo4j Cypher for compliance and audit support |

## 🏗️ System Architecture

```mermaid
flowchart LR
    subgraph "Data Ingestion"
        A[Infura API] --> B[Kafka Broker]
        A1[Sepolia Testnet] --> B
    end
    
    subgraph "CEP Layer"
        B --> C[Flink SQL Engine]
        C --> D[Pattern Detection]
        D --> E[85% Benign Filtering]
    end
    
    subgraph "Graph Construction"
        E --> F[Spark Structured Streaming]
        F --> G[1-min Sliding Windows]
        G --> H[PyTorch Geometric Graphs]
    end
    
    subgraph "Hybrid Storage & Inference"
        H --> I[Neo4j Graph DB]
        H --> J[Google Colab TGAT]
        I --> K[Neo4j Spark Connector]
        J --> L[Anomaly Scores]
        L --> M[GNNExplainer]
    end
    
    subgraph "Analysis & Monitoring"
        I --> N[Investigative Queries]
        I --> O[Graph Visualization]
        M --> P[Real-time Dashboard]
        M --> Q[Alert System]
        N --> R[Compliance Reports]
    end
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style F fill:#e8f5e8
    style I fill:#fff8e1
    style J fill:#fff3e0
    style P fill:#fce4ec
```

## 🔧 Technology Stack

### **Core Components**
- **Data Ingestion**: Apache Kafka + Infura API (Java/Python)
- **Complex Event Processing**: Apache Flink SQL (Java-based, stateful streaming)
- **Graph Construction**: Apache Spark Structured Streaming (Scala/Python)
- **Graph Database**: Neo4j + Neo4j Spark Connector
- **Machine Learning**: PyTorch + PyTorch Geometric (Python)
- **Model**: TGAT (Temporal Graph Attention Network)
- **ML Deployment**: Google Colab (GPU acceleration for MacBook compatibility)
- **Explainability**: GNNExplainer (Python)
- **Deployment**: Docker + Cloud integration

### **🔀 Hybrid Architecture Strategy**

**Spark Pipeline:**
- Handles real-time graph construction for ML pipeline
- Produces 1-minute sliding window snapshots
- Exports to PyTorch Geometric → TGAT inference

**Neo4j Storage:**
- Stores cumulative transaction graph for persistence
- Enables visual queries, audits, and investigative support
- Provides compliance reporting and historical analysis

**Integration Point:**
After Spark constructs each 1-min graph window:
- ➜ Export to PyG → Google Colab TGAT inference
- ➜ Append to Neo4j using Neo4j Spark Connector

### **Why Hybrid Architecture?**
- **🚀 Real-time Performance**: Spark optimized for streaming ML inference
- **🔍 Investigative Power**: Neo4j native graph queries and visualization
- **� Compliance**: Persistent storage for regulatory requirements
- **🔄 Best of Both**: Stream processing + graph database capabilities
- **� MacBook Compatibility**: Cloud-based GPU inference via Colab

### **Infrastructure**
- **Orchestration**: Docker Compose + Google Colab integration
- **Monitoring**: Custom dashboards + Neo4j Browser
- **Data Storage**: Neo4j persistent + in-memory checkpointing
- **API Integration**: Infura Ethereum API + Colab REST endpoints

## 📊 Performance Targets

| Metric | Target | Current Status |
|--------|--------|----------------|
| **Inference Latency** | <1 second | 🎯 Target |
| **Graph Construction** | <200ms per snapshot | 🎯 Target |
| **Neo4j Ingestion** | <100ms per window | 🎯 Target |
| **Throughput** | >1000 tx/s | 🎯 Target |
| **CEP Filtering** | >85% benign removal | 🎯 Target |
| **Explainability** | >90% interpretability | 🎯 Target |
| **Model Performance** | >80% F1 Score | 🎯 Target |
| **Graph Queries** | <500ms investigative queries | 🎯 Target |
| **Resource Usage** | <70% CPU/Memory | 🎯 Target |

## 📚 Datasets

### **1. Elliptic Bitcoin Dataset** (Training)
- **Source**: Kaggle public dataset
- **Records**: 203,769 transactions
- **Features**: 166 features per transaction
- **Structure**: Directed graph (nodes=transactions, edges=bitcoin flows)
- **Files**:
  - `elliptic_txs_features.csv` - Transaction features
  - `elliptic_txs_classes.csv` - Transaction labels (illicit/licit/unknown)
  - `elliptic_txs_edgelist.csv` - Transaction connections

### **2. Live Ethereum Data** (Evaluation)
- **Source**: Infura API + Sepolia Testnet
- **Type**: Continuous streaming data
- **Features**: >20 transaction attributes
- **Structure**: Real-time transaction flows

## 🚀 Quick Start

### Prerequisites
```bash
# Required software
- Docker & Docker Compose
- Python 3.8+
- Java 11+ (required for Flink CEP stateful processing)
- Google Colab account (for TGAT inference)
- 16GB+ RAM
- Maven 3.6+ (for Java CEP components)
```

### Installation
```bash
# Clone repository
git clone https://github.com/yourusername/real-time-blockchain-aml.git
cd real-time-blockchain-aml

# Set up environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your Infura API key and Colab endpoints
```

### Quick Start
```bash
# Start core infrastructure (including Neo4j)
docker-compose up -d zookeeper kafka flink neo4j

# Start the pipeline
docker-compose up -d --build

# Monitor services
docker-compose ps
```

### Access Interfaces
- **Kafka UI**: http://localhost:8080
- **Flink Dashboard**: http://localhost:8081  
- **Neo4j Browser**: http://localhost:7474
- **AML Dashboard**: http://localhost:3000
- **API Docs**: http://localhost:8000/docs

## 🏗️ Project Structure

```
real-time-blockchain-aml/
├── 📁 ingestion/              # Kafka + Infura integration
│   ├── infura_client.py       # Ethereum API client
│   ├── kafka_producer.py      # Transaction streaming
│   └── config/                # API configuration
├── 📁 cep/                    # Complex Event Processing (Java-based)
│   ├── flink_sql_jobs/        # Stateful CEP pattern detection (Java)
│   ├── pattern_rules.sql      # AML rule definitions (SQL)
│   ├── java_operators/        # Custom Flink operators (Java)
│   └── deployment/            # Flink cluster configuration
├── 📁 graph/                  # Graph construction layer
│   ├── spark_streaming.py     # Windowed graph builder (1-min windows)
│   ├── graph_builder.py       # Transaction → Graph conversion
│   ├── neo4j_connector.py     # Neo4j Spark Connector integration
│   └── optimization/          # Performance tuning
├── 📁 neo4j/                  # Neo4j graph database layer
│   ├── graph_ingestion.py     # Spark → Neo4j data pipeline
│   ├── cypher_queries/        # AML investigation queries
│   ├── graph_visualization.py # Neo4j Browser integration
│   └── compliance_reports.py  # Regulatory reporting
├── 📁 gnn/                    # TGAT model layer (Colab deployment)
│   ├── tgat_model.py         # Model implementation
│   ├── training/             # Elliptic dataset training
│   ├── colab_inference.py    # Google Colab API integration
│   ├── api_client.py         # Colab REST client
│   └── checkpoints/          # Trained models
├── 📁 explainer/             # GNNExplainer integration
│   ├── gnn_explainer.py     # Attribution analysis
│   ├── visualization.py     # Explanation interfaces
│   └── evaluation/          # Interpretability testing
├── 📁 dashboard/             # Monitoring & visualization
│   ├── frontend/            # Web interface
│   ├── api/                 # REST endpoints
│   ├── neo4j_dashboard/     # Graph visualization components
│   └── monitoring/          # System health
├── 📁 tutorials/             # Learning materials
│   ├── simple_streaming_demo.py      # Basic concepts
│   ├── neo4j_graph_tutorial.py       # Graph database operations
│   ├── colab_integration_guide.py    # Cloud ML setup
│   └── hybrid_architecture_demo.py   # End-to-end pipeline
├── 📁 scripts/               # Utility scripts
├── 📁 tests/                 # Test suite
├── docker-compose.yml        # Service orchestration (with Neo4j)
└── requirements.txt          # Python dependencies
```

## 🔬 Research Methodology

### **Development Approach**
Hybrid containerized architecture using Docker Compose orchestration with six interconnected layers:

1. **📥 Data Ingestion Layer**: Kafka broker + Infura API integration with asyncio
2. **🔍 Complex Event Processing**: Flink SQL (Java-based) for stateful pattern detection with configurable CEP rules
3. **📊 Graph Construction**: Spark Structured Streaming with PyTorch Geometric (1-min windows)
4. **🗄️ Graph Storage**: Neo4j persistent storage with Spark Connector integration
5. **🧠 ML Inference**: TGAT model with Google Colab GPU deployment and API serving
6. **📱 Visualization**: Real-time dashboard + Neo4j Browser with explainability interface

### **Evaluation Framework**

#### **Accuracy Assessment**
- **Metrics**: Precision, Recall, F1-Score (focus on minority class)
- **Validation**: Time-split cross-validation to prevent data leakage
- **Thresholds**: ROC-AUC and PR-AUC analysis

#### **Performance Evaluation**
- **Latency**: End-to-end transaction → alert timing
- **Throughput**: System capacity under varying loads
- **Scalability**: Stress testing with synthetic transaction generation
- **Graph Performance**: Neo4j query response times and ingestion rates

#### **Explainability Testing**
- **User Studies**: >90% interpretability validation
- **Attribution Quality**: Node/edge importance accuracy
- **Visualization**: Real-time explanation delivery via Neo4j Browser

## 📈 Expected Outcomes

### **Performance Benchmarks**
- ✅ **85%+ benign transaction filtering** at CEP stage
- ✅ **<200ms graph construction** with 1-min sliding windows
- ✅ **<100ms Neo4j ingestion** per graph window
- ✅ **<1 second inference** with Google Colab GPU deployment
- ✅ **80%+ F1 score** on Elliptic dataset benchmarks
- ✅ **>90% explainability** in user evaluations
- ✅ **>1000 tx/s throughput** with <70% resource usage
- ✅ **<500ms investigative queries** in Neo4j

### **Research Impact**
1. **🏦 Financial Institutions**: Faster money laundering detection with investigative capabilities
2. **🏛️ Regulatory Compliance**: Streamlined audits, FATF directive support, and persistent audit trails
3. **🔬 Academic Contribution**: Blueprint for hybrid streaming GNN + graph database architecture
4. **🌐 Broader Impact**: Enhanced financial stability and reduced illicit capital flows
5. **🔍 Investigative Enhancement**: Real-time graph queries for compliance teams

## 🔍 Research Gaps Addressed

| Research Gap | Our Solution | Innovation |
|--------------|--------------|------------|
| **No end-to-end CEP+GNN** | Kafka-Flink-Spark-TGAT pipeline | First integrated streaming system |
| **Missing streaming explainability** | Real-time GNNExplainer + Neo4j visualization | Per-alert attribution analysis |
| **Batch-only GNN inference** | Cloud-based TGAT serving (Colab) | Sub-second temporal graph inference |
| **Rigid CEP rules** | Configurable Flink SQL patterns | Adaptive rule-based filtering |
| **No graph persistence** | Neo4j hybrid storage | Investigative queries + compliance |
| **MacBook ML limitations** | Google Colab GPU integration | Cross-platform ML deployment |

## 🤝 Contributing

We welcome contributions! See our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Setup
```bash
# Development installation
pip install -r requirements-dev.txt

# Run tests
pytest tests/

# Code quality
black . && flake8 . && mypy .

# Neo4j local setup
docker run -d --name neo4j -p 7474:7474 -p 7687:7687 neo4j:latest
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

### **Academic References**
- **TGAT**: [Temporal Graph Attention Network](https://arxiv.org/abs/2002.07962)
- **GNNExplainer**: [GNNExplainer: Generating Explanations for Graph Neural Networks](https://arxiv.org/abs/1903.03894)
- **Elliptic Dataset**: [The Elliptic Data Set](https://www.kaggle.com/ellipticco/elliptic-data-set)

### **Technology Stack**
- [Apache Kafka](https://kafka.apache.org/) - Distributed streaming platform
- [Apache Flink](https://flink.apache.org/) - Stream processing framework
- [Apache Spark](https://spark.apache.org/) - Unified analytics engine
- [Neo4j](https://neo4j.com/) - Graph database platform
- [PyTorch Geometric](https://pytorch-geometric.readthedocs.io/) - Graph neural networks
- [Infura](https://infura.io/) - Ethereum infrastructure
- [Google Colab](https://colab.research.google.com/) - Cloud GPU platform

### **Research Community**
Special thanks to the blockchain AML research community and the authors whose work inspired this hybrid architecture approach.

---

## 📞 Contact

- **Principal Investigator**: [Your Name]
- **Institution**: [Your University/Organization]
- **Email**: [<EMAIL>]
- **Project Repository**: [GitHub Link]

---

*This project represents cutting-edge research in hybrid real-time blockchain AML, bridging the gap between low-latency Complex Event Processing, batch-processed Graph Neural Networks, and persistent graph storage for comprehensive compliance and investigative support.*
