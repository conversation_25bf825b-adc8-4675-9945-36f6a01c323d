services:
  zookeeper:
    container_name: zookeeper-integrated
    image: confluentinc/cp-zookeeper:7.3.0
    platform: linux/arm64
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - blockchain-aml-network
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 3

  kafka:
    container_name: kafka-integrated
    image: confluentinc/cp-kafka:7.3.0
    platform: linux/arm64
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_LOG_RETENTION_HOURS: 24
      KAFKA_LOG_RETENTION_BYTES: **********
    networks:
      - blockchain-aml-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:29092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 3

  kafka-ui:
    container_name: kafka-ui-integrated
    image: provectuslabs/kafka-ui:latest
    platform: linux/arm64
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: blockchain-aml
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    depends_on:
      kafka:
        condition: service_healthy
    networks:
      - blockchain-aml-network

  kafka-init:
    container_name: kafka-init-integrated
    image: confluentinc/cp-kafka:7.3.0
    platform: linux/arm64
    depends_on:
      kafka:
        condition: service_healthy
    command: >
      bash -c "
        echo 'Waiting for Kafka to be ready...'
        cub kafka-ready -b kafka:29092 1 30
        echo 'Creating Kafka topics...'
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic ethereum-transactions
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic filtered-transactions
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic graph-snapshots
        echo 'Kafka topics created successfully'
        kafka-topics --bootstrap-server kafka:29092 --list
      "
    networks:
      - blockchain-aml-network

  # Create additional Kafka topics for CEP processing
  kafka-cep-topics:
    container_name: kafka-cep-topics-init
    image: confluentinc/cp-kafka:7.3.0
    platform: linux/arm64
    depends_on:
      kafka:
        condition: service_healthy
    command: >
      bash -c "
        echo '📋 Creating CEP-specific Kafka topics...'
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 6 --replication-factor 1 --topic filtered-transactions
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic suspicious-alerts
        kafka-topics --create --if-not-exists --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 --topic cep-metrics
        echo '✅ CEP topics created successfully'
        kafka-topics --bootstrap-server kafka:29092 --list | grep -E '(filtered|suspicious|cep)'
      "
    networks:
      - blockchain-aml-network

  sepolia-ingestion:
    container_name: sepolia-ingestion-integrated
    build:
      context: ./ingestion        # Tell Docker to use the ingestion folder as build context
      dockerfile: dockerfile      # The dockerfile is located within the ingestion context
    platform: linux/arm64
    depends_on:
      kafka:
        condition: service_healthy
      kafka-init:
        condition: service_completed_successfully
    restart: unless-stopped
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=${KAFKA_BOOTSTRAP_SERVERS:-kafka:29092}
      - INFURA_URL=${INFURA_URL}
      - KAFKA_TRANSACTIONS_TOPIC=ethereum-transactions
      - ETHEREUM_NETWORK=sepolia
      - INGESTION_MODE=live
      - BATCH_SIZE=5
      - LOG_LEVEL=DEBUG
      - INFURA_REQUEST_INTERVAL=1.0
      - INFURA_BATCH_SIZE=3
      - INFURA_MAX_RETRIES=5
      - PYTHONUNBUFFERED=1
    networks:
      - blockchain-aml-network

  flink-jobmanager:
    container_name: flink-jobmanager-cep
    image: flink:1.17-scala_2.12-java11
    platform: linux/arm64
    ports:
      - "8081:8081"
    command: jobmanager
    environment:
      - FLINK_PROPERTIES=jobmanager.rpc.address=flink-jobmanager
      - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
      # Memory optimization for CEP processing
      - FLINK_CONF_jobmanager_memory_process_size=2048m  
      - FLINK_CONF_taskmanager_memory_process_size=4096m
      # Enable checkpointing for fault tolerance
      - FLINK_CONF_state_backend=filesystem
      - FLINK_CONF_state_checkpoints_dir=file:///tmp/flink-checkpoints
      - FLINK_CONF_execution_checkpointing_interval=30000
      # Parallelism for high throughput
      - FLINK_CONF_parallelism_default=4
    volumes:
      - flink_data:/tmp/flink-checkpoints
    networks:
      - blockchain-aml-network
    depends_on:
      kafka:
        condition: service_healthy

  flink-taskmanager:
    image: flink:1.17-scala_2.12-java11
    platform: linux/arm64
    depends_on:
      - flink-jobmanager
    command: taskmanager
    scale: 2  # Scale for parallel processing
    environment:
      - FLINK_PROPERTIES=jobmanager.rpc.address=flink-jobmanager
      - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
      # Optimized memory configuration for CEP
      - FLINK_CONF_taskmanager_memory_process_size=4096m
      - FLINK_CONF_taskmanager_memory_flink_size=3200m
      - FLINK_CONF_taskmanager_numberOfTaskSlots=4
      # State management for pattern storage
      - FLINK_CONF_state_backend=filesystem
      - FLINK_CONF_state_checkpoints_dir=file:///tmp/flink-checkpoints
    volumes:
      - flink_data:/tmp/flink-checkpoints
    networks:
      - blockchain-aml-network

  # CEP Processor deployment service
  cep-processor:
    container_name: blockchain-cep-processor
    build:
      context: ./cep
      dockerfile: Dockerfile
    platform: linux/arm64
    depends_on:
      - flink-jobmanager
      - flink-taskmanager
      - kafka
    environment:
      - FLINK_JOBMANAGER_HOST=flink-jobmanager
      - FLINK_JOBMANAGER_PORT=8081
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      # CEP filtering configuration
      - CEP_FILTERING_TARGET_RATIO=0.85
      - CEP_THROUGHPUT_TARGET=1000
      - CEP_LATENCY_TARGET_MS=200
      # Pattern detection thresholds
      - NORMAL_VALUE_THRESHOLD_ETH=10.0
      - HIGH_VALUE_THRESHOLD_ETH=100.0
      - RAPID_TX_THRESHOLD_MINUTES=2
      - GAS_LIMIT_NORMAL=21000
    networks:
      - blockchain-aml-network
    restart: unless-stopped

  # CEP Monitoring service
  cep-monitor:
    container_name: cep-filtering-monitor
    build:
      context: ./monitoring
      dockerfile: dockerfile
    platform: linux/arm64
    ports:
      - "8082:8082"  # Monitoring dashboard
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - FLINK_REST_URL=http://flink-jobmanager:8081
      - MONITORING_INTERVAL_SECONDS=30
      - TARGET_FILTERING_RATIO=0.85
    depends_on:
      - flink-jobmanager
      - kafka
      - cep-processor
    networks:
      - blockchain-aml-network

volumes:
  flink_data:
    driver: local

networks:
  blockchain-aml-network:
    driver: bridge