#!/bin/bash

# startup.sh - Containerized startup script for CEP deployment
set -euo pipefail  # Exit on error, undefined vars, and pipe failures

echo "🚀 Starting Flink CEP Processor deployment..."

# Configuration from environment variables
FLINK_JOBMANAGER_HOST=${FLINK_JOBMANAGER_HOST:-flink-jobmanager}
FLINK_JOBMANAGER_PORT=${FLINK_JOBMANAGER_PORT:-8081}
KAFKA_BOOTSTRAP_SERVERS=${KAFKA_BOOTSTRAP_SERVERS:-kafka:29092}
MAX_WAIT_TIME=${MAX_WAIT_TIME:-300}  # 5 minutes max wait

# Function to check if a service is ready
check_service() {
    local service_name=$1
    local check_command=$2
    local max_attempts=60
    local attempt=1
    
    echo "⏳ Waiting for $service_name..."
    while [ $attempt -le $max_attempts ]; do
        if eval "$check_command" > /dev/null 2>&1; then
            echo "✅ $service_name is ready"
            return 0
        fi
        echo "   Waiting for $service_name... (attempt $attempt/$max_attempts)"
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to become ready after $((max_attempts * 5)) seconds"
    return 1
}

# Wait for Flink JobManager to be ready
check_service "Flink JobManager" "curl -f http://$FLINK_JOBMANAGER_HOST:$FLINK_JOBMANAGER_PORT/overview"

# Wait for Kafka to be ready
check_service "Kafka" "echo 'test' | kafkacat -P -b $KAFKA_BOOTSTRAP_SERVERS -t test-connectivity || kafka-topics --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --list"

# Verify required topics exist
echo "🔍 Verifying Kafka topics..."
REQUIRED_TOPICS=("ethereum-transactions" "filtered-transactions" "suspicious-alerts")

for topic in "${REQUIRED_TOPICS[@]}"; do
    if kafka-topics --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --list 2>/dev/null | grep -q "^$topic$"; then
        echo "   ✅ Topic '$topic' exists"
    else
        echo "   ⚠️  Topic '$topic' not found - it should be created by kafka-init service"
        # Don't exit here, as topics might be auto-created
    fi
done

# Check if JAR file exists
JAR_PATH="/opt/flink/lib/flink-cep-processor.jar"
if [ ! -f "$JAR_PATH" ]; then
    echo "❌ JAR file not found at $JAR_PATH"
    echo "Available files in /opt/flink/lib/:"
    ls -la /opt/flink/lib/ | grep -E "\.(jar|JAR)$" || echo "No JAR files found"
    exit 1
fi

echo "✅ JAR file found: $JAR_PATH"

# Submit the CEP job to Flink
echo "🔧 Submitting CEP job to Flink cluster..."

# Use flink run command with proper configuration
flink run \
    --class com.blockchain.aml.cep.FlinkCEPProcessor \
    --parallelism 4 \
    --jobmanager $FLINK_JOBMANAGER_HOST:$FLINK_JOBMANAGER_PORT \
    "$JAR_PATH"

if [ $? -eq 0 ]; then
    echo "✅ CEP job submitted successfully"
else
    echo "❌ Failed to submit CEP job"
    exit 1
fi

# Get the job ID for monitoring
echo "📊 Retrieving job information..."
sleep 5  # Give job time to register

JOB_ID=$(curl -s http://$FLINK_JOBMANAGER_HOST:$FLINK_JOBMANAGER_PORT/jobs/overview | \
         jq -r '.jobs[] | select(.name=="Blockchain AML CEP Processor") | .jid' | head -n1)

if [ "$JOB_ID" != "null" ] && [ -n "$JOB_ID" ]; then
    echo "✅ Job registered with ID: $JOB_ID"
    echo "🔗 Job URL: http://$FLINK_JOBMANAGER_HOST:$FLINK_JOBMANAGER_PORT/#/job/$JOB_ID/overview"
else
    echo "⚠️  Could not retrieve job ID immediately"
fi

# Keep container running and monitor job status
echo "📊 Monitoring CEP job status... (Press Ctrl+C to stop monitoring)"

monitor_job() {
    while true; do
        sleep 30
        
        # Get current job status
        JOB_STATUS=$(curl -s http://$FLINK_JOBMANAGER_HOST:$FLINK_JOBMANAGER_PORT/jobs/overview 2>/dev/null | \
                     jq -r '.jobs[] | select(.name=="Blockchain AML CEP Processor") | .state' 2>/dev/null | head -n1)
        
        case "$JOB_STATUS" in
            "RUNNING")
                echo "✅ $(date): CEP job is running normally"
                ;;
            "FAILED"|"CANCELED")
                echo "❌ $(date): CEP job has failed with status: $JOB_STATUS"
                echo "🔍 Checking for job exceptions..."
                if [ -n "$JOB_ID" ]; then
                    curl -s http://$FLINK_JOBMANAGER_HOST:$FLINK_JOBMANAGER_PORT/jobs/$JOB_ID/exceptions | jq '.' || echo "Could not retrieve exceptions"
                fi
                echo "🔄 Job monitoring will continue in case of restart..."
                ;;
            "")
                echo "⚠️  $(date): No job found - may be starting up or restarting"
                ;;
            *)
                echo "ℹ️  $(date): CEP job status: $JOB_STATUS"
                ;;
        esac
    done
}

# Handle graceful shutdown
trap 'echo "🛑 Received shutdown signal, stopping monitoring..."; exit 0' SIGTERM SIGINT

# Start monitoring in background and wait
monitor_job &
MONITOR_PID=$!

# Wait for the monitoring process or a signal
wait $MONITOR_PID