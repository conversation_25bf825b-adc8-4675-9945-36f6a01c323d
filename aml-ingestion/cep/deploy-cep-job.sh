# deploy-cep-job.sh - Manual deployment script
#!/bin/bash

set -e

echo "🚀 Deploying Flink CEP Processor for Blockchain AML"
echo "=================================================="

# Configuration
FLINK_JOBMANAGER=${FLINK_JOBMANAGER_HOST:-localhost}
FLINK_PORT=${FLINK_JOBMANAGER_PORT:-8081}
KAFKA_BOOTSTRAP=${KAFKA_BOOTSTRAP_SERVERS:-kafka:29092}

echo "📋 Configuration:"
echo "   Flink JobManager: $FLINK_JOBMANAGER:$FLINK_PORT"
echo "   Kafka Bootstrap: $KAFKA_BOOTSTRAP"
echo ""

# Step 1: Build the project
echo "🔨 Building Flink CEP Processor..."
mvn clean package -DskipTests
echo "✅ Build completed"

# Step 2: Check Flink cluster status
echo "🔍 Checking Flink cluster status..."
FLINK_STATUS=$(curl -s http://$FLINK_JOBMANAGER:$FLINK_PORT/overview | jq -r '.taskmanagers')
echo "   Available TaskManagers: $FLINK_STATUS"

if [ "$FLINK_STATUS" = "null" ] || [ "$FLINK_STATUS" = "0" ]; then
    echo "❌ No TaskManagers available. Please start Flink cluster first."
    exit 1
fi
echo "✅ Flink cluster is ready"

# Step 3: Check Kafka topics
echo "🔍 Checking required Kafka topics..."
REQUIRED_TOPICS=("ethereum-transactions" "filtered-transactions" "suspicious-alerts")

for topic in "${REQUIRED_TOPICS[@]}"; do
    if kafka-topics --bootstrap-server $KAFKA_BOOTSTRAP --list | grep -q "^$topic$"; then
        echo "   ✅ Topic '$topic' exists"
    else
        echo "   ⚠️  Creating topic '$topic'..."
        kafka-topics --bootstrap-server $KAFKA_BOOTSTRAP \
                    --create --topic $topic \
                    --partitions 3 --replication-factor 1
        echo "   ✅ Topic '$topic' created"
    fi
done

# Step 4: Submit the job
echo "🚀 Submitting CEP job to Flink..."
JOB_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -F "jarfile=@target/flink-cep-processor-1.0.0.jar" \
    http://$FLINK_JOBMANAGER:$FLINK_PORT/jars/upload)

JAR_ID=$(echo $JOB_RESPONSE | jq -r '.filename' | sed 's/.*\///')

if [ "$JAR_ID" = "null" ]; then
    echo "❌ Failed to upload JAR file"
    echo "Response: $JOB_RESPONSE"
    exit 1
fi

echo "   ✅ JAR uploaded with ID: $JAR_ID"

# Run the job
echo "▶️  Starting CEP job..."
RUN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{
        "entryClass": "com.blockchain.aml.cep.FlinkCEPProcessor",
        "parallelism": 4,
        "programArgs": "",
        "savepointPath": null
    }' \
    http://$FLINK_JOBMANAGER:$FLINK_PORT/jars/$JAR_ID/run)

JOB_ID=$(echo $RUN_RESPONSE | jq -r '.jobid')

if [ "$JOB_ID" = "null" ]; then
    echo "❌ Failed to start job"
    echo "Response: $RUN_RESPONSE"
    exit 1
fi

echo "✅ CEP job started with ID: $JOB_ID"

# Step 5: Monitor job startup
echo "📊 Monitoring job startup..."
for i in {1..30}; do
    sleep 2
    JOB_STATUS=$(curl -s http://$FLINK_JOBMANAGER:$FLINK_PORT/jobs/$JOB_ID | jq -r '.state')
    
    case $JOB_STATUS in
        "RUNNING")
            echo "✅ Job is now RUNNING successfully!"
            break
            ;;
        "CREATED"|"RESTARTING"|"INITIALIZING")
            echo "   ⏳ Job status: $JOB_STATUS (attempt $i/30)"
            ;;
        "FAILED"|"CANCELED")
            echo "❌ Job failed with status: $JOB_STATUS"
            # Get job exceptions
            curl -s http://$FLINK_JOBMANAGER:$FLINK_PORT/jobs/$JOB_ID/exceptions | jq '.'
            exit 1
            ;;
    esac
done

if [ "$JOB_STATUS" != "RUNNING" ]; then
    echo "⚠️  Job took longer than expected to start. Current status: $JOB_STATUS"
    echo "   Check Flink dashboard at http://$FLINK_JOBMANAGER:$FLINK_PORT"
fi

# Step 6: Display monitoring information
echo ""
echo "🎉 CEP Deployment Complete!"
echo "=========================="
echo "📊 Monitoring URLs:"
echo "   Flink Dashboard: http://$FLINK_JOBMANAGER:$FLINK_PORT"
echo "   Job Details: http://$FLINK_JOBMANAGER:$FLINK_PORT/#/job/$JOB_ID/overview"
echo ""
echo "📈 Expected Performance:"
echo "   • Filtering Ratio: >85% benign transactions"
echo "   • Throughput: >1000 tx/s"
echo "   • Latency: <200ms per transaction"
echo ""
echo "🔧 Kafka Topics:"
echo "   • Input: ethereum-transactions"
echo "   • Output: filtered-transactions" 
echo "   • Alerts: suspicious-alerts"
echo ""
echo "📝 To monitor filtering efficiency:"
echo "   python3 cep_monitoring.py"
echo ""
echo "✅ Deployment successful! CEP layer is now filtering blockchain transactions."
