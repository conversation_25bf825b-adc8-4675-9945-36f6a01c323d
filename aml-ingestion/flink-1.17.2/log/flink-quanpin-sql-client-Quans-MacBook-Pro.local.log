2025-07-26 21:32:30,445 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: taskmanager.memory.process.size, 1728m
2025-07-26 21:32:30,445 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: jobmanager.bind-host, localhost
2025-07-26 21:32:30,445 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: taskmanager.bind-host, localhost
2025-07-26 21:32:30,445 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: taskmanager.host, localhost
2025-07-26 21:32:30,445 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: parallelism.default, 1
2025-07-26 21:32:30,445 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: jobmanager.execution.failover-strategy, region
2025-07-26 21:32:30,445 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: jobmanager.rpc.address, localhost
2025-07-26 21:32:30,445 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: taskmanager.numberOfTaskSlots, 1
2025-07-26 21:32:30,446 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: rest.address, localhost
2025-07-26 21:32:30,446 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: jobmanager.memory.process.size, 1600m
2025-07-26 21:32:30,446 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: jobmanager.rpc.port, 6123
2025-07-26 21:32:30,446 INFO  org.apache.flink.configuration.GlobalConfiguration           [] - Loading configuration property: rest.bind-address, localhost
2025-07-26 21:32:30,454 ERROR org.apache.flink.table.client.SqlClient                      [] - SQL Client must stop. Unexpected exception. This is a bug. Please consider filing an issue.
java.lang.NoClassDefFoundError: org/apache/hadoop/conf/Configuration
	at java.lang.Class.getDeclaredConstructors0(Native Method) ~[?:?]
	at java.lang.Class.privateGetDeclaredConstructors(Class.java:3006) ~[?:?]
	at java.lang.Class.getConstructor0(Class.java:3211) ~[?:?]
	at java.lang.Class.getConstructor(Class.java:2201) ~[?:?]
	at java.util.ServiceLoader.getConstructor(ServiceLoader.java:623) ~[?:?]
	at java.util.ServiceLoader$LazyClassPathLookupIterator.hasNextService(ServiceLoader.java:1111) ~[?:?]
	at java.util.ServiceLoader$LazyClassPathLookupIterator.hasNext(ServiceLoader.java:1142) ~[?:?]
	at java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164) ~[?:?]
	at java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246) ~[?:?]
	at org.apache.flink.client.deployment.DefaultClusterClientServiceLoader.getApplicationModeTargetNames(DefaultClusterClientServiceLoader.java:101) ~[flink-dist-1.17.2.jar:1.17.2]
	at org.apache.flink.client.cli.GenericCLI.getApplicationModeTargetNames(GenericCLI.java:148) ~[flink-dist-1.17.2.jar:1.17.2]
	at org.apache.flink.client.cli.GenericCLI.<init>(GenericCLI.java:75) ~[flink-dist-1.17.2.jar:1.17.2]
	at org.apache.flink.client.cli.CliFrontend.loadCustomCommandLines(CliFrontend.java:1247) ~[flink-dist-1.17.2.jar:1.17.2]
	at org.apache.flink.table.gateway.service.context.DefaultContext.load(DefaultContext.java:151) ~[flink-sql-gateway-1.17.2.jar:1.17.2]
	at org.apache.flink.table.client.gateway.DefaultContextUtils.buildDefaultContext(DefaultContextUtils.java:54) ~[flink-sql-client-1.17.2.jar:1.17.2]
	at org.apache.flink.table.client.SqlClient.start(SqlClient.java:106) ~[flink-sql-client-1.17.2.jar:1.17.2]
	at org.apache.flink.table.client.SqlClient.startClient(SqlClient.java:228) [flink-sql-client-1.17.2.jar:1.17.2]
	at org.apache.flink.table.client.SqlClient.main(SqlClient.java:179) [flink-sql-client-1.17.2.jar:1.17.2]
Caused by: java.lang.ClassNotFoundException: org.apache.hadoop.conf.Configuration
	at jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:580) ~[?:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:490) ~[?:?]
	... 18 more
