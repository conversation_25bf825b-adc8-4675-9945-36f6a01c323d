// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- org.apache.logging.log4j:log4j-api:2.17.1
- org.apache.logging.log4j:log4j-core:2.17.1
- org.apache.logging.log4j:log4j-slf4j-impl:2.17.1
- org.apache.logging.log4j:log4j-1.2-api:2.17.1

This project bundles the following dependencies under the BSD license.
See bundled license files for details.

- cloudpickle:2.2.0

Flink : Examples : Table
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Table
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Batch
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Batch
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Batch
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Table
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Streaming
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Batch
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Batch
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Streaming
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Streaming
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-examples-streaming-state-machine
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0. (http://www.apache.org/licenses/LICENSE-2.0.txt)

- org.apache.kafka:kafka-clients:3.2.3


Flink : Connectors : Kafka
Copyright 2014-2023 The Apache Software Foundation


Flink : Examples : Table
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Table
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Streaming
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Batch
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Table
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Batch
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Streaming
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Batch
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Flink : Examples : Table
Copyright 2014-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Apache Log4j SLF4J Binding
Copyright 1999-1969 The Apache Software Foundation

Apache Log4j API
Copyright 1999-1969 The Apache Software Foundation

Apache Log4j Core
Copyright 1999-2012 Apache Software Foundation

ResolverUtil.java
Copyright 2005-2006 Tim Fennell

Apache Log4j 1.x Compatibility API
Copyright 1999-1969 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-azure-fs-hadoop
Copyright 2014-2022 The Apache Software Foundation

This project includes software developed at
The Apache Software Foundation (http://www.apache.org/).

This project bundles the following dependencies under the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.fasterxml.jackson.core:jackson-core:2.13.4
- com.google.guava:guava:20.0
- commons-codec:commons-codec:1.15
- commons-logging:commons-logging:1.1.3
- org.apache.hadoop.thirdparty:hadoop-shaded-guava:1.1.1
- org.apache.hadoop:hadoop-azure:3.3.4
- org.apache.httpcomponents:httpclient:4.5.13
- org.apache.httpcomponents:httpcore:4.4.14
- org.codehaus.jackson:jackson-core-asl:1.9.13
- org.codehaus.jackson:jackson-mapper-asl:1.9.13
- org.eclipse.jetty:jetty-util-ajax:9.3.24.v20180605
- org.eclipse.jetty:jetty-util:9.3.24.v20180605
- org.wildfly.openssl:wildfly-openssl:1.0.7.Final

This project bundles the following dependencies under the MIT (https://opensource.org/licenses/MIT)

- com.microsoft.azure:azure-keyvault-core:1.0.0
- com.microsoft.azure:azure-storage:7.0.1

The bundled Apache Hadoop Relocated (Shaded) Third-party Miscellaneous Libs
org.apache.hadoop.thirdparty:hadoop-shaded-guava dependency bundles the following dependencies under
the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.google.guava:guava:30.1.1-jre


Flink : FileSystems : Hadoop FS
Copyright 2014-2023 The Apache Software Foundation

flink-fs-hadoop-shaded
Copyright 2014-2022 The Apache Software Foundation

- com.fasterxml.jackson.core:jackson-annotations:2.13.4
- com.fasterxml.jackson.core:jackson-core:2.13.4
- com.fasterxml.jackson.core:jackson-databind:********
- com.fasterxml.woodstox:woodstox-core:5.3.0
- com.google.guava:failureaccess:1.0
- com.google.guava:guava:27.0-jre
- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
- com.google.j2objc:j2objc-annotations:1.1
- commons-beanutils:commons-beanutils:1.9.4
- commons-collections:commons-collections:3.2.2
- commons-io:commons-io:2.11.0
- commons-logging:commons-logging:1.1.3
- org.apache.commons:commons-compress:1.21
- org.apache.commons:commons-configuration2:2.1.1
- org.apache.commons:commons-lang3:3.12.0
- org.apache.commons:commons-text:1.10.0
- org.apache.hadoop.thirdparty:hadoop-shaded-guava:1.1.1
- org.apache.hadoop.thirdparty:hadoop-shaded-protobuf_3_7:1.1.1
- org.apache.hadoop:hadoop-annotations:3.3.4
- org.apache.hadoop:hadoop-auth:3.3.4
- org.apache.hadoop:hadoop-common:3.3.4
- org.apache.kerby:kerb-core:1.0.1
- org.apache.kerby:kerby-asn1:1.0.1
- org.apache.kerby:kerby-pkix:1.0.1
- org.apache.kerby:kerby-util:1.0.1
- org.xerial.snappy:snappy-java:1.1.10.4

- org.checkerframework:checker-qual:2.5.2
- org.codehaus.mojo:animal-sniffer-annotations:1.17

This project bundles the following dependencies under BSD-2 License (https://opensource.org/licenses/BSD-2-Clause).
See bundled license files for details.

- dnsjava:dnsjava:2.1.7

This project bundles the following dependencies under the Go License (https://golang.org/LICENSE).
See bundled license files for details.

- com.google.re2j:re2j:1.1

This project bundles the following dependencies under BSD License (https://opensource.org/licenses/bsd-license.php).
See bundled license files for details.

- org.codehaus.woodstox:stax2-api:4.2.1 (https://github.com/FasterXML/stax2-api/tree/stax2-api-4.2.1)

Apache Hadoop
Copyright 2006 and onwards The Apache Software Foundation.

Export Control Notice
---------------------

This distribution includes cryptographic software.  The country in
which you currently reside may have restrictions on the import,
possession, use, and/or re-export to another country, of
encryption software.  BEFORE using any encryption software, please
check your country's laws, regulations and policies concerning the
import, possession, or use, and re-export of encryption software, to
see if this is permitted.  See <http://www.wassenaar.org/> for more
information.

The U.S. Government Department of Commerce, Bureau of Industry and
Security (BIS), has classified this software as Export Commodity
Control Number (ECCN) 5D002.C.1, which includes information security
software using or performing cryptographic functions with asymmetric
algorithms.  The form and manner of this Apache Software Foundation
distribution makes it eligible for export under the License Exception
ENC Technology Software Unrestricted (TSU) exception (see the BIS
Export Administration Regulations, Section 740.13) for both object
code and source code.

The following provides more details on the included cryptographic software:

This software uses the SSL libraries from the Jetty project written
by mortbay.org.
Hadoop Yarn Server Web Proxy uses the BouncyCastle Java
cryptography APIs written by the Legion of the Bouncy Castle Inc.

Apache Hadoop Third-party Libs
Copyright 2020 and onwards The Apache Software Foundation.

Apache Commons IO
Copyright 2002-2021 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (https://www.apache.org/).

Apache Commons Collections
Copyright 2001-2015 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

Apache Commons Logging
Copyright 2003-2013 The Apache Software Foundation

Apache Commons BeanUtils
Copyright 2000-2019 The Apache Software Foundation

Apache Commons Configuration
Copyright 2001-2017 The Apache Software Foundation

Apache Commons Lang
Copyright 2001-2021 The Apache Software Foundation

Apache Commons Text
Copyright 2014-2022 The Apache Software Foundation

Apache Commons Compress
Copyright 2002-2021 The Apache Software Foundation

---

The files in the package org.apache.commons.compress.archivers.sevenz
were derived from the LZMA SDK, version 9.20 (C/ and CPP/7zip/),
which has been placed in the public domain:

"LZMA SDK is placed in the public domain." (http://www.7-zip.org/sdk.html)

The test file lbzip2_32767.bz2 has been copied from libbzip2's source
repository:

This program, "bzip2", the associated library "libbzip2", and all
documentation, are copyright (C) 1996-2019 Julian R Seward.  All
rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

2. The origin of this software must not be misrepresented; you must 
   not claim that you wrote the original software.  If you use this 
   software in a product, an acknowledgment in the product 
   documentation would be appreciated but is not required.

3. Altered source versions must be plainly marked as such, and must
   not be misrepresented as being the original software.

4. The name of the author may not be used to endorse or promote 
   products derived from this software without specific prior written 
   permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Julian Seward, <EMAIL>

Kerby-kerb core
Copyright 2014-2017 The Apache Software Foundation

Kerby PKIX Project
Copyright 2014-2017 The Apache Software Foundation

Kerby ASN1 Project
Copyright 2014-2017 The Apache Software Foundation

Kerby Util
Copyright 2014-2017 The Apache Software Foundation

# Jackson JSON processor

Jackson is a high-performance, Free/Open Source JSON processing library.
It was originally written by Tatu Saloranta (<EMAIL>), and has
been in development since 2007.
It is currently developed by a community of developers.

## Licensing

Jackson 2.x core and extension components are licensed under Apache License 2.0
To find the details that apply to this artifact see the accompanying LICENSE file.

## Credits

A list of contributors may be found from CREDITS(-2.x) file, which is included
in some artifacts (usually source distributions); but is always available
from the source code management (SCM) system project uses.

Apache HttpClient
Copyright 1999-2020 The Apache Software Foundation

Apache HttpCore
Copyright 2005-2020 The Apache Software Foundation

Apache Commons Codec
Copyright 2002-2020 The Apache Software Foundation

src/test/org/apache/commons/codec/language/DoubleMetaphoneTest.java
contains test data from http://aspell.net/test/orig/batch0.tab.
Copyright (C) 2002 Kevin Atkinson (<EMAIL>)

===============================================================================

The content of package org.apache.commons.codec.language.bm has been translated
from the original php source code available at http://stevemorse.org/phoneticinfo.htm
with permission from the original authors.
Original source copyright:
Copyright (c) 2008 Alexander Beider & Stephen P. Morse.

This product currently only contains code developed by authors
of specific components, as identified by the source code files;
if such notes are missing files have been created by
Tatu Saloranta.

For additional credits (generally to people who reported problems)
see CREDITS file.

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Libraries : CEP
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Libraries : CEP Scala
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Connectors : Files
Copyright 2014-2023 The Apache Software Foundation

Flink : Connectors : Base
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Formats : Csv
Copyright 2014-2023 The Apache Software Foundation

Flink : Format : Common
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-dist
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.google.code.findbugs:jsr305:1.3.9
- com.twitter:chill-java:0.7.6
- com.ververica:frocksdbjni:6.20.3-ververica-2.0
- commons-cli:commons-cli:1.5.0
- commons-collections:commons-collections:3.2.2
- commons-io:commons-io:2.11.0
- org.apache.commons:commons-compress:1.21
- org.apache.commons:commons-lang3:3.12.0
- org.apache.commons:commons-math3:3.6.1
- org.apache.commons:commons-text:1.10.0
- org.javassist:javassist:3.24.0-GA
- org.lz4:lz4-java:1.8.0
- org.objenesis:objenesis:2.1
- org.xerial.snappy:snappy-java:1.1.10.4

This project bundles the following dependencies under the BSD license.
See bundled license files for details.

- com.esotericsoftware.kryo:kryo:2.24.0
- com.esotericsoftware.minlog:minlog:1.2

This project bundles the following dependencies under the MIT/X11 license.
See bundled license files for details.

- org.slf4j:slf4j-api:1.7.36

This project bundles the following dependencies under the CDDL 1.1 license.
See bundled license files for details.

- javax.activation:javax.activation-api:1.2.0
- javax.xml.bind:jaxb-api:2.3.1


Flink : Core
Copyright 2014-2023 The Apache Software Foundation

Flink : Annotations
Copyright 2014-2023 The Apache Software Foundation

Apache Flink-shaded
Copyright 2006-2022 The Apache Software Foundation

flink-shaded-asm9
Copyright 2014-2021 The Apache Software Foundation

- org.ow2.asm:asm:9.3
- org.ow2.asm:asm-analysis:9.3
- org.ow2.asm:asm-commons:9.3
- org.ow2.asm:asm-tree:9.3

flink-shaded-jackson
Copyright 2014-2021 The Apache Software Foundation

This project includes software developed at
The Apache Software Foundation (http://www.apache.org/).

- com.fasterxml.jackson.core:jackson-annotations:2.13.4
- com.fasterxml.jackson.core:jackson-core:2.13.4
- com.fasterxml.jackson.core:jackson-databind:2.13.4
- com.fasterxml.jackson.dataformat:jackson-dataformat-csv:2.13.4
- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.13.4
- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.13.4
- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.4
- org.yaml:snakeyaml:1.31

# Jackson JSON processor

Jackson is a high-performance, Free/Open Source JSON processing library.
It was originally written by Tatu Saloranta (<EMAIL>), and has
been in development since 2007.
It is currently developed by a community of developers.

## Licensing

Jackson 2.x core and extension components are licensed under Apache License 2.0
To find the details that apply to this artifact see the accompanying LICENSE file.

## Credits

A list of contributors may be found from CREDITS(-2.x) file, which is included
in some artifacts (usually source distributions); but is always available
from the source code management (SCM) system project uses.

Jackson is a high-performance, Free/Open Source JSON processing library.
It was originally written by Tatu Saloranta (<EMAIL>), and has
been in development since 2007.
It is currently developed by a community of developers, as well as supported
commercially by FasterXML.com.

Jackson core and extension components may be licensed under different licenses.
To find the details that apply to this artifact see the accompanying LICENSE file.
For more information, including possible other licensing options, contact
FasterXML.com (http://fasterxml.com).

A list of contributors may be found from CREDITS file, which is included
in some artifacts (usually source distributions); but is always available
from the source code management (SCM) system project uses.

Apache Commons Lang
Copyright 2001-2021 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (https://www.apache.org/).

Apache Commons Text
Copyright 2014-2022 The Apache Software Foundation

Apache Commons Collections
Copyright 2001-2015 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

Apache Commons Compress
Copyright 2002-2021 The Apache Software Foundation

---

The files in the package org.apache.commons.compress.archivers.sevenz
were derived from the LZMA SDK, version 9.20 (C/ and CPP/7zip/),
which has been placed in the public domain:

"LZMA SDK is placed in the public domain." (http://www.7-zip.org/sdk.html)

The test file lbzip2_32767.bz2 has been copied from libbzip2's source
repository:

This program, "bzip2", the associated library "libbzip2", and all
documentation, are copyright (C) 1996-2019 Julian R Seward.  All
rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

2. The origin of this software must not be misrepresented; you must 
   not claim that you wrote the original software.  If you use this 
   software in a product, an acknowledgment in the product 
   documentation would be appreciated but is not required.

3. Altered source versions must be plainly marked as such, and must
   not be misrepresented as being the original software.

4. The name of the author may not be used to endorse or promote 
   products derived from this software without specific prior written 
   permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Julian Seward, <EMAIL>

flink-shaded-guava-30
Copyright 2014-2021 The Apache Software Foundation

- com.google.guava:guava:30.1.1-jre
- com.google.guava:failureaccess:1.0.1

Flink : Java
Copyright 2014-2023 The Apache Software Foundation

Apache Commons Math
Copyright 2001-2016 The Apache Software Foundation

This product includes software developed for Orekit by
CS Systèmes d'Information (http://www.c-s.fr/)
Copyright 2010-2012 CS Systèmes d'Information

flink-runtime
Copyright 2014-2021 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0. (http://www.apache.org/licenses/LICENSE-2.0.txt)

- io.airlift:aircompressor:0.21

Flink : RPC : Core
Copyright 2014-2023 The Apache Software Foundation

Flink : RPC : Akka-Loader
Copyright 2014-2023 The Apache Software Foundation

flink-rpc-akka
Copyright 2014-2022 The Apache Software Foundation

- com.hierynomus:asn-one:0.5.0
- com.typesafe:config:1.4.2
- com.typesafe:ssl-config-core_2.12:0.4.3
- com.typesafe.akka:akka-actor_2.12:2.6.20
- com.typesafe.akka:akka-remote_2.12:2.6.20
- com.typesafe.akka:akka-pki_2.12:2.6.20
- com.typesafe.akka:akka-protobuf-v3_2.12:2.6.20
- com.typesafe.akka:akka-slf4j_2.12:2.6.20
- com.typesafe.akka:akka-stream_2.12:2.6.20
- io.netty:netty:3.10.6.Final
- org.agrona:agrona:1.15.1
- org.scala-lang:scala-library:2.12.16
- org.scala-lang.modules:scala-parser-combinators_2.12:1.1.2

The following dependencies all share the same BSD license which you find under licenses/LICENSE.scala.

- org.scala-lang.modules:scala-java8-compat_2.12:0.8.0

This project bundles the following dependencies under the Creative Commons CC0 "No Rights Reserved".

- org.reactivestreams:reactive-streams:1.0.3

This project bundles io.netty:netty:3.10.6.Final from which it inherits the following notices:

This product contains the extensions to Java Collections Framework which has
been derived from the works by JSR-166 EG, Doug Lea, and Jason T. Greene:

  * LICENSE:
    * licenses/LICENSE.jsr166y (Public Domain)
  * HOMEPAGE:
    * http://gee.cs.oswego.edu/cgi-bin/viewcvs.cgi/jsr166/
    * http://viewvc.jboss.org/cgi-bin/viewvc.cgi/jbosscache/experimental/jsr166/

This product contains a modified version of Robert Harder's Public Domain
Base64 Encoder and Decoder, which can be obtained at:

  * LICENSE:
    * licenses/LICENSE.base64 (Public Domain)
  * HOMEPAGE:
    * http://iharder.sourceforge.net/current/java/base64/

This product contains a modified version of 'JZlib', a re-implementation of
zlib in pure Java, which can be obtained at:

  * LICENSE:
    * licenses/LICENSE.jzlib (BSD Style License)
  * HOMEPAGE:
    * http://www.jcraft.com/jzlib/

This product contains a modified version of 'Webbit', a Java event based
WebSocket and HTTP server:

  * LICENSE:
    * licenses/LICENSE.webbit (BSD License)
  * HOMEPAGE:
    * https://github.com/joewalnes/webbit

Scala
Copyright (c) 2002-2022 EPFL
Copyright (c) 2011-2022 Lightbend, Inc.

Scala includes software developed at
LAMP/EPFL (https://lamp.epfl.ch/) and
Lightbend, Inc. (https://www.lightbend.com/).

Licensed under the Apache License, Version 2.0 (the "License").
Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

This software includes projects with other licenses -- see `doc/LICENSE.md`.

Flink : Queryable state : Client Java
Copyright 2014-2023 The Apache Software Foundation

Apache Commons IO
Copyright 2002-2021 The Apache Software Foundation

flink-shaded-netty
Copyright 2014-2021 The Apache Software Foundation

- io.netty:netty-all:4.1.82.Final
- io.netty:netty-buffer:4.1.82.Final
- io.netty:netty-codec:4.1.82.Final
- io.netty:netty-codec-dns:4.1.82.Final
- io.netty:netty-codec-haproxy:4.1.82.Final
- io.netty:netty-codec-http:4.1.82.Final
- io.netty:netty-codec-http2:4.1.82.Final
- io.netty:netty-codec-memcache:4.1.82.Final
- io.netty:netty-codec-mqtt:4.1.82.Final
- io.netty:netty-codec-redis:4.1.82.Final
- io.netty:netty-codec-smtp:4.1.82.Final
- io.netty:netty-codec-socks:4.1.82.Final
- io.netty:netty-codec-stomp:4.1.82.Final
- io.netty:netty-codec-xml:4.1.82.Final
- io.netty:netty-common:4.1.82.Final
- io.netty:netty-handler:4.1.82.Final
- io.netty:netty-handler-proxy:4.1.82.Final
- io.netty:netty-resolver:4.1.82.Final
- io.netty:netty-resolver-dns:4.1.82.Final
- io.netty:netty-resolver-dns-classes-macos:4.1.82.Final
- io.netty:netty-resolver-dns-native-macos:osx-x86_64:4.1.82.Final
- io.netty:netty-resolver-dns-native-macos:osx-aarch_64:4.1.82.Final
- io.netty:netty-transport:4.1.82.Final
- io.netty:netty-transport-classes-epoll:4.1.82.Final
- io.netty:netty-transport-classes-kqueue:4.1.82.Final
- io.netty:netty-transport-native-epoll:linux-x86_64:4.1.82.Final
- io.netty:netty-transport-native-epoll:linux-aarch_64:4.1.82.Final
- io.netty:netty-transport-native-kqueue:osx-x86_64:4.1.82.Final
- io.netty:netty-transport-native-kqueue:osx-aarch_64:4.1.82.Final
- io.netty:netty-transport-native-unix-common:4.1.82.Final
- io.netty:netty-transport-rxtx:4.1.82.Final
- io.netty:netty-transport-sctp:4.1.82.Final
- io.netty:netty-transport-udt:4.1.82.Final

flink-shaded-zookeeper-3
Copyright 2014-2021 The Apache Software Foundation

- com.google.guava:guava:27.0.1-jre
- io.dropwizard.metrics:metrics-core:4.1.12.1
- io.netty:netty-buffer:4.1.82.Final
- io.netty:netty-codec:4.1.82.Final
- io.netty:netty-common:4.1.82.Final
- io.netty:netty-handler:4.1.82.Final
- io.netty:netty-resolver:4.1.82.Final
- io.netty:netty-transport:4.1.82.Final
- io.netty:netty-transport-classes-epoll:4.1.82.Final
- io.netty:netty-transport-native-epoll:4.1.82.Final
- io.netty:netty-transport-native-unix-common:4.1.82.Final
- org.apache.curator:curator-client:5.3.0
- org.apache.curator:curator-framework:5.3.0
- org.apache.curator:curator-recipes:5.3.0
- org.apache.zookeeper:zookeeper:3.7.1
- org.apache.zookeeper:zookeeper-jute:3.7.1

Curator Recipes
Copyright 2011-2022 The Apache Software Foundation

Curator Framework
Copyright 2011-2022 The Apache Software Foundation

Curator Client
Copyright 2011-2022 The Apache Software Foundation

Apache Commons CLI
Copyright 2002-2021 The Apache Software Foundation

flink-runtime-web
Copyright 2014-2022 The Apache Software Foundation

@angular/animations 13.1.3 : MIT License
@angular/cdk 13.1.3 : MIT License
@angular/common 13.1.3 : MIT License
@angular/compiler 13.1.3 : MIT License
@angular/core 13.1.3 : MIT License
@angular/forms 13.1.3 : MIT License
@angular/platform-browser 13.1.3 : MIT License
@angular/platform-browser-dynamic 13.1.3 : MIT License
@angular/router 13.1.3 : MIT License
@antv/adjust 0.2.3 : MIT License
@antv/attr 0.3.2 : MIT License
@antv/component 0.8.20 : MIT License
@antv/coord 0.3.1 : MIT License
@antv/g2 4.1.34 : MIT License
@antv/scale 0.3.14 : MIT License
@antv/util 2.0.17 : ISC License
ansi-regex 5.0.1 : MIT License
balanced-match 1.0.2 : MIT License
brace-expansion 1.1.11 : MIT License
camelcase 5.3.1 : MIT License
Chalk 2.4.2 : MIT License
cliui 7.0.4 : ISC License
Commander.js 7.2.0 : MIT License
core-js v3.19.1 : MIT License
cpettitt/graphlib 2.1.8 : MIT License
d3-array 3.1.1 : BSD 3-clause "New" or "Revised" License
d3-axis 3.0.0 : BSD 3-clause "New" or "Revised" License
d3-brush 3.0.0 : BSD 3-clause "New" or "Revised" License
d3-chord 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-collection 1.0.7 : BSD 3-clause "New" or "Revised" License
d3-color 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-contour 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-delaunay 6.0.2 : BSD 3-clause "New" or "Revised" License
d3-dispatch 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-drag 3.0.0 : BSD 3-clause "New" or "Revised" License
d3-dsv 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-ease 1.0.7 : BSD 3-clause "New" or "Revised" License
d3-fetch 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-force 3.0.0 : BSD 3-clause "New" or "Revised" License
d3-format 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-geo 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-hierarchy 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-interpolate v1.4.0 : BSD 3-clause "New" or "Revised" License
d3-path 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-polygon v3.0.1 : BSD 3-clause "New" or "Revised" License
d3-quadtree 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-random 3.0.1 : BSD 3-clause "New" or "Revised" License
d3-scale 4.0.2 : BSD 3-clause "New" or "Revised" License
d3-scale-chromatic 3.0.0 : BSD 3-clause "New" or "Revised" License
d3-selection v3.0.0 : BSD 3-clause "New" or "Revised" License
d3-shape v3.0.1 : BSD 3-clause "New" or "Revised" License
d3-time 3.0.0 : BSD 3-clause "New" or "Revised" License
d3-time-format 4.0.0 : BSD 3-clause "New" or "Revised" License
d3-timer v1.0.10 : BSD 3-clause "New" or "Revised" License
d3-transition v3.0.1 : BSD 3-clause "New" or "Revised" License
d3-zoom 3.0.0 : BSD 3-clause "New" or "Revised" License
D3.js 7.1.1 : BSD 3-clause "New" or "Revised" License
dagre 0.8.5 : MIT License
Decamelize 1.2.0 : MIT License
define-properties v1.1.3 : MIT License
es-abstract 1.19.1 : MIT License
es-to-primitive 1.2.1 : MIT License
escape-string-regexp 1.0.5 : MIT License
fecha 4.2.1 : MIT License
fs.realpath 1.0.0 : ISC License
function-bind 1.1.1 : MIT License
has 1.0.3 : MIT License
has-ansi 2.0.0 : MIT License
has-symbols 1.0.2 : MIT License
iconv-lite v0.6.3 : MIT License
inflight 1.0.6 : ISC License
inherits 2.0.4 : ISC License
is-buffer 1.1.6 : MIT License
is-callable 1.2.4 : MIT License
is-date-object 1.0.5 : MIT License
is-regex 1.1.4 : MIT License
is-symbol 1.0.4 : MIT License
isaacs/once 1.4.0 : ISC License
kind-of 6.0.3 : MIT License
kossnocorp/date-fns 2.25.0 : MIT License
Lo-Dash 4.17.21 : MIT License
minimatch 3.0.4 : ISC License
minimist 1.2.5 : MIT License
monaco-editor 0.31.1 : MIT License
ng-zorro-antd 13.0.1 : MIT License
object-inspect 1.11.0 : MIT License
object-keys 1.1.1 : MIT License
parse5 6.0.1 : MIT License
path-is-absolute 1.0.1 : MIT License
path-parse 1.0.7 : MIT License
repeat-string 1.6.1 : MIT License
RESOLVE v1.20.0 : MIT License
rw 1.3.3 : BSD 3-clause "New" or "Revised" License
RxJS 6.6.7 : Apache License 2.0
	 Copyright (c) 2015-2018 Google, Inc., Netflix, Inc., Microsoft Corp. and contributors

	No NOTICE file was provided by RxJS
safer-buffer 2.1.2 : MIT License
sindresorhus/ansi-styles 3.2.1 : MIT License
sindresorhus/supports-color 5.5.0 : MIT License
source-map 0.7.3 : BSD 3-clause "New" or "Revised" License
string.prototype.trimend 1.0.4 : MIT License
string.prototype.trimstart 1.0.4 : MIT License
Strip ANSI 6.0.0 : MIT License
through 2.3.8 : MIT License
TinyColor 3.4.0 : MIT License
tslib 2.3.1 : Apache License 2.0
	Copyright (c) Microsoft Corporation. All rights reserved.

	No NOTICE file was provided.
wrappy 1.0.2 : ISC License
yargs 17.2.1 : MIT License
Zone.js v0.11.4 : MIT License

Licenses:

Apache License 2.0
(RxJS 6.6.7, tslib 2.3.0)

Apache License
Version 2.0, January 2004
=========================

http://www.apache.org/licenses/

TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

1. Definitions.

"License" shall mean the terms and conditions for use, reproduction, and
distribution as defined by Sections 1 through 9 of this document.

"Licensor" shall mean the copyright owner or entity authorized by the copyright
owner that is granting the License.

"Legal Entity" shall mean the union of the acting entity and all other entities
that control, are controlled by, or are under common control with that entity.
For the purposes of this definition, "control" means (i) the power, direct or
indirect, to cause the direction or management of such entity, whether by
contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the
outstanding shares, or (iii) beneficial ownership of such entity.

"You" (or "Your") shall mean an individual or Legal Entity exercising permissions
granted by this License.

"Source" form shall mean the preferred form for making modifications, including
but not limited to software source code, documentation source, and configuration
files.

"Object" form shall mean any form resulting from mechanical transformation or
translation of a Source form, including but not limited to compiled object code,
generated documentation, and conversions to other media types.

"Work" shall mean the work of authorship, whether in Source or Object form, made
available under the License, as indicated by a copyright notice that is included
in or attached to the work (an example is provided in the Appendix below).

"Derivative Works" shall mean any work, whether in Source or Object form, that is
based on (or derived from) the Work and for which the editorial revisions,
annotations, elaborations, or other modifications represent, as a whole, an
original work of authorship. For the purposes of this License, Derivative Works
shall not include works that remain separable from, or merely link (or bind by
name) to the interfaces of, the Work and Derivative Works thereof.

"Contribution" shall mean any work of authorship, including the original version
of the Work and any modifications or additions to that Work or Derivative Works
thereof, that is intentionally submitted to Licensor for inclusion in the Work by
the copyright owner or by an individual or Legal Entity authorized to submit on
behalf of the copyright owner. For the purposes of this definition, "submitted"
means any form of electronic, verbal, or written communication sent to the
Licensor or its representatives, including but not limited to communication on
electronic mailing lists, source code control systems, and issue tracking systems
that are managed by, or on behalf of, the Licensor for the purpose of discussing
and improving the Work, but excluding communication that is conspicuously marked
or otherwise designated in writing by the copyright owner as "Not a
Contribution."

"Contributor" shall mean Licensor and any individual or Legal Entity on behalf of
whom a Contribution has been received by Licensor and subsequently incorporated
within the Work.

2. Grant of Copyright License. Subject to the terms and conditions of this
License, each Contributor hereby grants to You a perpetual, worldwide,
non-exclusive, no-charge, royalty-free, irrevocable copyright license to
reproduce, prepare Derivative Works of, publicly display, publicly perform,
sublicense, and distribute the Work and such Derivative Works in Source or Object
form.

3. Grant of Patent License. Subject to the terms and conditions of this License,
each Contributor hereby grants to You a perpetual, worldwide, non-exclusive,
no-charge, royalty-free, irrevocable (except as stated in this section) patent
license to make, have made, use, offer to sell, sell, import, and otherwise
transfer the Work, where such license applies only to those patent claims
licensable by such Contributor that are necessarily infringed by their
Contribution(s) alone or by combination of their Contribution(s) with the Work to
which such Contribution(s) was submitted. If You institute patent litigation
against any entity (including a cross-claim or counterclaim in a lawsuit)
alleging that the Work or a Contribution incorporated within the Work constitutes
direct or contributory patent infringement, then any patent licenses granted to
You under this License for that Work shall terminate as of the date such
litigation is filed.

4. Redistribution. You may reproduce and distribute copies of the Work or
Derivative Works thereof in any medium, with or without modifications, and in
Source or Object form, provided that You meet the following conditions:

  a. You must give any other recipients of the Work or Derivative Works a copy of
    this License; and

  b. You must cause any modified files to carry prominent notices stating that
    You changed the files; and

  c. You must retain, in the Source form of any Derivative Works that You
    distribute, all copyright, patent, trademark, and attribution notices from
    the Source form of the Work, excluding those notices that do not pertain to
    any part of the Derivative Works; and

  d. If the Work includes a "NOTICE" text file as part of its distribution, then
    any Derivative Works that You distribute must include a readable copy of the
    attribution notices contained within such NOTICE file, excluding those
    notices that do not pertain to any part of the Derivative Works, in at least
    one of the following places: within a NOTICE text file distributed as part of
    the Derivative Works; within the Source form or documentation, if provided
    along with the Derivative Works; or, within a display generated by the
    Derivative Works, if and wherever such third-party notices normally appear.
    The contents of the NOTICE file are for informational purposes only and do
    not modify the License. You may add Your own attribution notices within
    Derivative Works that You distribute, alongside or as an addendum to the
    NOTICE text from the Work, provided that such additional attribution notices
    cannot be construed as modifying the License.

You may add Your own copyright statement to Your modifications and may provide
additional or different license terms and conditions for use, reproduction, or
distribution of Your modifications, or for any such Derivative Works as a whole,
provided Your use, reproduction, and distribution of the Work otherwise complies
with the conditions stated in this License.

5. Submission of Contributions. Unless You explicitly state otherwise, any
Contribution intentionally submitted for inclusion in the Work by You to the
Licensor shall be under the terms and conditions of this License, without any
additional terms or conditions. Notwithstanding the above, nothing herein shall
supersede or modify the terms of any separate license agreement you may have
executed with Licensor regarding such Contributions.

6. Trademarks. This License does not grant permission to use the trade names,
trademarks, service marks, or product names of the Licensor, except as required
for reasonable and customary use in describing the origin of the Work and
reproducing the content of the NOTICE file.

7. Disclaimer of Warranty. Unless required by applicable law or agreed to in
writing, Licensor provides the Work (and each Contributor provides its
Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied, including, without limitation, any warranties or
conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
PARTICULAR PURPOSE. You are solely responsible for determining the
appropriateness of using or redistributing the Work and assume any risks
associated with Your exercise of permissions under this License.

8. Limitation of Liability. In no event and under no legal theory, whether in
tort (including negligence), contract, or otherwise, unless required by
applicable law (such as deliberate and grossly negligent acts) or agreed to in
writing, shall any Contributor be liable to You for damages, including any
direct, indirect, special, incidental, or consequential damages of any character
arising as a result of this License or out of the use or inability to use the
Work (including but not limited to damages for loss of goodwill, work stoppage,
computer failure or malfunction, or any and all other commercial damages or
losses), even if such Contributor has been advised of the possibility of such
damages.

9. Accepting Warranty or Additional Liability. While redistributing the Work or
Derivative Works thereof, You may choose to offer, and charge a fee for,
acceptance of support, warranty, indemnity, or other liability obligations and/or
rights consistent with this License. However, in accepting such obligations, You
may act only on Your own behalf and on Your sole responsibility, not on behalf of
any other Contributor, and only if You agree to indemnify, defend, and hold each
Contributor harmless for any liability incurred by, or claims asserted against,
such Contributor by reason of your accepting any such warranty or additional
liability.

END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work

To apply the Apache License to your work, attach the following boilerplate
notice, with the fields enclosed by brackets "[]" replaced with your own
identifying information. (Don't include the brackets!) The text should be
enclosed in the appropriate comment syntax for the file format. We also recommend
that a file or class name and description of purpose be included on the same
"printed page" as the copyright notice for easier identification within
third-party archives.

  Copyright [yyyy] [name of copyright owner] Licensed under the Apache License,
  Version 2.0 (the "License"); you may not use this file except in compliance
  with the License. You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law
  or agreed to in writing, software distributed under the License is
  distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied. See the License for the specific language
  governing permissions and limitations under the License.

BSD 3-clause "New" or "Revised" License
(d3-array 3.1.1, d3-axis 3.0.0, d3-brush 3.0.0, d3-chord 3.0.1, d3-collection 1.0.7, d3-color 3.0.1, d3-contour 3.0.1, d3-delaunay 6.0.2, d3-dispatch 3.0.1, d3-drag 3.0.0, d3-dsv 3.0.1, d3-ease 1.0.7, d3-fetch 3.0.1, d3-force 3.0.0, d3-format 3.0.1, d3-geo 3.0.1, d3-hierarchy 3.0.1, d3-interpolate v1.4.0, d3-path 3.0.1, d3-polygon v3.0.1, d3-quadtree 3.0.1, d3-random 3.0.1, d3-scale 4.0.2, d3-scale-chromatic 3.0.0, d3-selection v3.0.0, d3-shape v3.0.1, d3-time 3.0.0, d3-time-format 4.0.0, d3-timer v1.0.10, d3-transition v3.0.1, d3-zoom 3.0.0, D3.js 7.1.1)

Copyright (c) <YEAR>, <OWNER>
All rights reserved.

Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

  * Redistributions of source code must retain the above copyright notice, this
    list of conditions and the following disclaimer.

  * Redistributions in binary form must reproduce the above copyright notice,
    this list of conditions and the following disclaimer in the documentation
    and/or other materials provided with the distribution.

  * Neither the name of the <ORGANIZATION> nor the names of its contributors may
    be used to endorse or promote products derived from this software without
    specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

BSD 3-clause "New" or "Revised" License
(source-map 0.7.3)

License: BSD-3-clause

Files: debian/*
Copyright: 2014 Leo Iannacone <<EMAIL>>
License: BSD-3-clause

Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:
 1. Redistributions of source code must retain the above copyright
    notice, this list of conditions and the following disclaimer.
 2. Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the
    documentation and/or other materials provided with the distribution.
 3. Neither the name of the University nor the names of its
    contributors may be used to endorse or promote products derived from
    this software without specific prior written permission.
 .
 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE HOLDERS OR
 CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE

BSD 3-clause "New" or "Revised" License
(rw 1.3.3)

Upstream-Contact: https://github.com/mbostock/rw/issues
Source: https://github.com/mbostock/rw

Files: *
Copyright: 2014-2016 Mike Bostock (http://bost.ocks.org/mike)
License: BSD-3-Clause

Files: debian/*
Copyright: 2017 Pirate Praveen <<EMAIL>>
License: BSD-3-Clause

Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:
 1. Redistributions of source code must retain the above copyright
    notice, this list of conditions and the following disclaimer.
 2. Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the
    documentation and/or other materials provided with the distribution.
 3. Neither the name of the University nor the names of its contributors
    may be used to endorse or promote products derived from this software
    without specific prior written permission.
 .
 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE HOLDERS OR
 CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE

ISC License
(cliui 7.0.4)

Copyright (c) 2015, Contributors

Permission to use, copy, modify, and/or distribute this software
for any purpose with or without fee is hereby granted, provided
that the above copyright notice and this permission notice
appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES
OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE
LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES
OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION,
ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE

ISC License
(@antv/util 2.0.17)

ISC License (ISCL)
==================

Copyright (c) 4-digit year, Company or Person's Name

Permission to use, copy, modify, and/or distribute this software for any purpose
with or without fee is hereby granted, provided that the above copyright notice
and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS
OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF
THIS SOFTWARE.

ISC License
(inflight 1.0.6)

The ISC License

Copyright (c) Isaac Z. Schlueter

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted, provided that the above
copyright notice and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR
IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE

ISC License
(inherits 2.0.4)

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE

ISC License
(fs.realpath 1.0.0, isaacs/once 1.4.0, minimatch 3.0.4)

Copyright (c) Isaac Z. Schlueter and Contributors

ISC License
(wrappy 1.0.2)

Upstream-Contact: https://github.com/npm/wrappy/issues
Source: https://github.com/npm/wrappy

Files: *
Copyright: 2015 Isaac Z. Schlueter <<EMAIL>> (http://blog.izs.me/)
License: ISC

Files: debian/*
Copyright: 2015 Thorsten Alteholz <<EMAIL>>
License: ISC

License: ISC

Permission to use, copy, modify, and/or distribute this software for any
 purpose with or without fee is hereby granted, provided that the above
 copyright notice and this permission notice appear in all copies.
 .
 THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE

MIT License
(Commander.js 7.2.0)

(The MIT License)

Copyright (c) 2011 TJ Holowaychuk <<EMAIL>>

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
'Software'), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE

MIT License
(Chalk 2.4.2)

2016, Mathias Behrle <<EMAIL>>
License: Expat

License: Expat

Permission is hereby granted, free of charge, to any person
 obtaining a copy of this software and associated documentation files
 (the "Software"), to deal in the Software without restriction,
 including without limitation the rights to use, copy, modify, merge,
 publish, distribute, sublicense, and/or sell copies of the Software,
 and to permit persons to whom the Software is furnished to do so,
 subject to the following conditions:
 .
 The above copyright notice and this permission notice shall be
 included in all copies or substantial portions of the Software.
 .
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
 BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
 ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE

MIT License
(Lo-Dash 4.17.21)

====

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE

MIT License
(iconv-lite v0.6.3)

Copyright (c) 2011 Alexander Shtuchkin

MIT License
(balanced-match 1.0.2)

Copyright (c) 2013 Julian Gruber &lt;<EMAIL>&gt;

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
of the Software, and to permit persons to whom the Software is furnished to do
so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE

MIT License
(has 1.0.3)

Copyright (c) 2013 Thiago de Arruda

Permission is hereby granted, free of charge, to any person
obtaining a copy of this software and associated documentation
files (the "Software"), to deal in the Software without
restriction, including without limitation the rights to use,
copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the
Software is furnished to do so, subject to the following
conditions:

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
OTHER DEALINGS IN THE SOFTWARE

MIT License
(parse5 6.0.1)

Copyright (c) 2013-2018 Ivan Nikulin (<EMAIL>, https://github.com/inikulin)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE

MIT License
(TinyColor 3.4.0)

Copyright (c), Brian Grinstead, http://briangrinstead.com

MIT License
(function-bind 1.1.1)

MIT License
(brace-expansion 1.1.11)

MIT License

Copyright (c) 2013 Julian Gruber <<EMAIL>>

MIT License
(has-symbols 1.0.2)

Copyright (c) 2016 Jordan Harband

MIT License
(safer-buffer 2.1.2)

Copyright (c) 2018 Nikita Skovoroda <<EMAIL>>

MIT License
(yargs 17.2.1)

MIT License
(through 2.3.8)

The MIT License

Copyright (c) 2011 Dominic Tarr

Permission is hereby granted, free of charge,
to any person obtaining a copy of this software and
associated documentation files (the "Software"), to
deal in the Software without restriction, including
without limitation the rights to use, copy, modify,
merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom
the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice
shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR
ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE

MIT License
(@angular/animations 13.1.3, @angular/cdk 13.1.3, @angular/common 13.1.3, @angular/compiler 13.1.3, @angular/core 13.1.3, @angular/forms 13.1.3, @angular/platform-browser 13.1.3, @angular/platform-browser-dynamic 13.1.3, @angular/router 13.1.3, @ant-design/icons-angular 13.0.2, @antv/adjust 0.1.1, @antv/attr 0.1.2, @antv/component 0.3.9, @antv/coord 0.1.0, @antv/g 3.4.10, @antv/g2 3.5.17, @antv/gl-matrix 2.7.1, @antv/scale 0.1.5, ant-design-palettes 1.2.13, contour_plot 0.0.1, core-js v2.6.12, cpettitt/graphlib 2.1.8, dagre 0.8.5, es-abstract 1.18.3, is-callable 1.2.3, kossnocorp/date-fns 2.130.1, monaco-editor 0.31.1, ng-zorro-antd 13.2.2, node-tape v4.14.0, object-keys 1.1.1, RESOLVE v1.20.0, Zone.js v0.11.4)

The MIT License
===============

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in the
Software without restriction, including without limitation the rights to use,
copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the
Software, and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN
AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

MIT License
(is-regex 1.1.4)

The MIT License (MIT)

Copyright (c) 2014 Jordan Harband

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
the Software, and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE

MIT License
(repeat-string 1.6.1)

Copyright (c) 2014-2016, Jon Schlinkert.

MIT License
(kind-of 6.0.3)

Copyright (c) 2014-2017, Jon Schlinkert

MIT License
(path-parse 1.0.7)

Copyright (c) 2015 Javier Blanco

MIT License
(define-properties v1.1.3)

Copyright (C) 2015 Jordan Harband

MIT License
(es-to-primitive 1.2.1, is-date-object 1.0.5, is-symbol 1.0.4, string.prototype.trimend 1.0.4, string.prototype.trimstart 1.0.4)

Copyright (c) 2015 Jordan Harband

MIT License
(fecha 4.2.1)

Copyright (c) 2015 Taylor Hakes

MIT License
(is-buffer 1.1.6)

Copyright (c) Feross Aboukhadijeh

MIT License
(ansi-regex 5.0.1, camelcase 5.3.1, Decamelize 1.2.0, escape-string-regexp 1.0.5, has-ansi 2.0.0, path-is-absolute 1.0.1, sindresorhus/ansi-styles 3.2.1, sindresorhus/supports-color 5.5.0, Strip ANSI 6.0.0)

Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)

MIT License
(minimist 1.2.5, object-inspect 1.11.0)

This software is released under the MIT license:

Flink : Optimizer
Copyright 2014-2023 The Apache Software Foundation

Flink : Clients
Copyright 2014-2023 The Apache Software Foundation

Flink : Streaming Java
Copyright 2014-2023 The Apache Software Foundation

Flink : Connectors : File Sink Common
Copyright 2014-2023 The Apache Software Foundation

Flink : Metrics : Core
Copyright 2014-2023 The Apache Software Foundation

Flink : Container
Copyright 2014-2023 The Apache Software Foundation

Flink : State backends : RocksDB
Copyright 2014-2023 The Apache Software Foundation

Flink : State backends : Changelog
Copyright 2014-2023 The Apache Software Foundation

Flink : State backends : Common
Copyright 2014-2023 The Apache Software Foundation

Flink : DSTL : DFS
Copyright 2014-2023 The Apache Software Foundation

flink-kubernetes
Copyright 2014-2022 The Apache Software Foundation

- com.fasterxml.jackson.core:jackson-annotations:2.13.4
- com.fasterxml.jackson.core:jackson-core:2.13.4
- com.fasterxml.jackson.core:jackson-databind:********
- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.13.4
- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.4
- com.squareup.okhttp3:logging-interceptor:3.14.9
- com.squareup.okhttp3:okhttp:3.14.9
- com.squareup.okio:okio:1.17.2
- io.fabric8:kubernetes-client:5.12.4
- io.fabric8:kubernetes-model-admissionregistration:5.12.4
- io.fabric8:kubernetes-model-apiextensions:5.12.4
- io.fabric8:kubernetes-model-apps:5.12.4
- io.fabric8:kubernetes-model-autoscaling:5.12.4
- io.fabric8:kubernetes-model-batch:5.12.4
- io.fabric8:kubernetes-model-certificates:5.12.4
- io.fabric8:kubernetes-model-common:5.12.4
- io.fabric8:kubernetes-model-coordination:5.12.4
- io.fabric8:kubernetes-model-core:5.12.4
- io.fabric8:kubernetes-model-discovery:5.12.4
- io.fabric8:kubernetes-model-events:5.12.4
- io.fabric8:kubernetes-model-extensions:5.12.4
- io.fabric8:kubernetes-model-flowcontrol:5.12.4
- io.fabric8:kubernetes-model-metrics:5.12.4
- io.fabric8:kubernetes-model-networking:5.12.4
- io.fabric8:kubernetes-model-node:5.12.4
- io.fabric8:kubernetes-model-policy:5.12.4
- io.fabric8:kubernetes-model-rbac:5.12.4
- io.fabric8:kubernetes-model-scheduling:5.12.4
- io.fabric8:kubernetes-model-storageclass:5.12.4
- io.fabric8:zjsonpatch:0.3.0
- org.yaml:snakeyaml:1.33

This project bundles the following dependencies under the BSD License.
See bundled license files for details.

- dk.brics.automaton:automaton:1.11-8

Note that publicsuffixes.gz is compiled from The Public Suffix List:
https://publicsuffix.org/list/public_suffix_list.dat

It is subject to the terms of the Mozilla Public License, v. 2.0:
https://mozilla.org/MPL/2.0/

Flink : Yarn
Copyright 2014-2023 The Apache Software Foundation

Flink : Connectors : Base
Copyright 2014-2023 The Apache Software Foundation

Flink : FileSystems : Hadoop FS
Copyright 2014-2023 The Apache Software Foundation

flink-shaded-force-shading
Copyright 2014-2022 The Apache Software Foundation

Objenesis
Copyright 2006-2013 Joe Walnes, Henri Tremblay, Leonardo Mesquita

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : External resources : GPU
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-gs-fs-hadoop
Copyright 2014-2022 The Apache Software Foundation

This project includes software developed at
The Apache Software Foundation (http://www.apache.org/).

This project bundles the following dependencies under the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.fasterxml.jackson.core:jackson-core:2.13.4
- com.google.android:annotations:4.1.1.4
- com.google.api-client:google-api-client-jackson2:1.32.2
- com.google.api-client:google-api-client:1.33.0
- com.google.api.grpc:grpc-google-cloud-storage-v2:2.0.1-alpha
- com.google.api.grpc:proto-google-cloud-storage-v2:2.0.1-alpha
- com.google.api.grpc:proto-google-common-protos:2.7.1
- com.google.api.grpc:proto-google-iam-v1:1.2.0
- com.google.apis:google-api-services-iamcredentials:v1-rev20210326-1.32.1
- com.google.apis:google-api-services-storage:v1-rev20211201-1.32.1
- com.google.auto.value:auto-value-annotations:1.9
- com.google.cloud.bigdataoss:gcs-connector:hadoop3-2.2.4
- com.google.cloud.bigdataoss:gcsio:2.2.4
- com.google.cloud.bigdataoss:util-hadoop:hadoop3-2.2.4
- com.google.cloud.bigdataoss:util:2.2.4
- com.google.cloud:google-cloud-core-http:2.3.5
- com.google.cloud:google-cloud-core:2.3.5
- com.google.cloud:google-cloud-storage:2.2.3
- com.google.code.gson:gson:2.8.9
- com.google.flogger:flogger-system-backend:0.7.1
- com.google.flogger:flogger:0.7.1
- com.google.flogger:google-extensions:0.7.1
- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
- com.google.http-client:google-http-client-apache-v2:1.41.0
- com.google.http-client:google-http-client-appengine:1.41.0
- com.google.http-client:google-http-client-gson:1.41.0
- com.google.http-client:google-http-client-jackson2:1.41.0
- com.google.http-client:google-http-client:1.41.0
- com.google.oauth-client:google-oauth-client:1.32.1
- commons-codec:commons-codec:1.15
- io.grpc:grpc-alts:1.43.2
- io.grpc:grpc-api:1.43.2
- io.grpc:grpc-auth:1.43.2
- io.grpc:grpc-context:1.43.2
- io.grpc:grpc-core:1.43.2
- io.grpc:grpc-grpclb:1.43.2
- io.grpc:grpc-netty-shaded:1.43.2
- io.grpc:grpc-protobuf-lite:1.43.2
- io.grpc:grpc-protobuf:1.43.2
- io.grpc:grpc-stub:1.43.2
- io.opencensus:opencensus-api:0.28.0
- io.opencensus:opencensus-contrib-http-util:0.28.0
- io.perfmark:perfmark-api:0.23.0
- org.apache.httpcomponents:httpclient:4.5.13
- org.apache.httpcomponents:httpcore:4.4.14
- org.conscrypt:conscrypt-openjdk-uber:2.5.1

This project bundles the following dependencies under BSD-2 License (https://opensource.org/licenses/BSD-2-Clause).
See bundled license files for details.

- com.google.api:api-common:2.1.2
- com.google.api:gax-httpjson:0.93.1
- com.google.api:gax:2.8.1

This project bundles the following dependencies under BSD-3 License (https://opensource.org/licenses/BSD-3-Clause).
See bundled license files for details.

- com.google.auth:google-auth-library-credentials:1.3.0
- com.google.auth:google-auth-library-oauth2-http:1.3.0
- com.google.protobuf:protobuf-java-util:3.19.2
- com.google.protobuf:protobuf-java:3.19.2
- org.threeten:threetenbp:1.5.2


Flink : FileSystems : Hadoop FS
Copyright 2014-2023 The Apache Software Foundation

flink-fs-hadoop-shaded
Copyright 2014-2022 The Apache Software Foundation

- com.fasterxml.jackson.core:jackson-annotations:2.13.4
- com.fasterxml.jackson.core:jackson-core:2.13.4
- com.fasterxml.jackson.core:jackson-databind:********
- com.fasterxml.woodstox:woodstox-core:5.3.0
- com.google.guava:failureaccess:1.0
- com.google.guava:guava:27.0-jre
- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
- com.google.j2objc:j2objc-annotations:1.1
- commons-beanutils:commons-beanutils:1.9.4
- commons-collections:commons-collections:3.2.2
- commons-io:commons-io:2.11.0
- commons-logging:commons-logging:1.1.3
- org.apache.commons:commons-compress:1.21
- org.apache.commons:commons-configuration2:2.1.1
- org.apache.commons:commons-lang3:3.12.0
- org.apache.commons:commons-text:1.10.0
- org.apache.hadoop.thirdparty:hadoop-shaded-guava:1.1.1
- org.apache.hadoop.thirdparty:hadoop-shaded-protobuf_3_7:1.1.1
- org.apache.hadoop:hadoop-annotations:3.3.4
- org.apache.hadoop:hadoop-auth:3.3.4
- org.apache.hadoop:hadoop-common:3.3.4
- org.apache.kerby:kerb-core:1.0.1
- org.apache.kerby:kerby-asn1:1.0.1
- org.apache.kerby:kerby-pkix:1.0.1
- org.apache.kerby:kerby-util:1.0.1
- org.xerial.snappy:snappy-java:1.1.10.4

This project bundles the following dependencies under the MIT (https://opensource.org/licenses/MIT)

- org.checkerframework:checker-qual:2.5.2
- org.codehaus.mojo:animal-sniffer-annotations:1.17

- dnsjava:dnsjava:2.1.7

This project bundles the following dependencies under the Go License (https://golang.org/LICENSE).
See bundled license files for details.

- com.google.re2j:re2j:1.1

This project bundles the following dependencies under BSD License (https://opensource.org/licenses/bsd-license.php).
See bundled license files for details.

- org.codehaus.woodstox:stax2-api:4.2.1 (https://github.com/FasterXML/stax2-api/tree/stax2-api-4.2.1)

Apache Hadoop
Copyright 2006 and onwards The Apache Software Foundation.

Export Control Notice
---------------------

This distribution includes cryptographic software.  The country in
which you currently reside may have restrictions on the import,
possession, use, and/or re-export to another country, of
encryption software.  BEFORE using any encryption software, please
check your country's laws, regulations and policies concerning the
import, possession, or use, and re-export of encryption software, to
see if this is permitted.  See <http://www.wassenaar.org/> for more
information.

The U.S. Government Department of Commerce, Bureau of Industry and
Security (BIS), has classified this software as Export Commodity
Control Number (ECCN) 5D002.C.1, which includes information security
software using or performing cryptographic functions with asymmetric
algorithms.  The form and manner of this Apache Software Foundation
distribution makes it eligible for export under the License Exception
ENC Technology Software Unrestricted (TSU) exception (see the BIS
Export Administration Regulations, Section 740.13) for both object
code and source code.

The following provides more details on the included cryptographic software:

This software uses the SSL libraries from the Jetty project written
by mortbay.org.
Hadoop Yarn Server Web Proxy uses the BouncyCastle Java
cryptography APIs written by the Legion of the Bouncy Castle Inc.

Apache Hadoop Third-party Libs
Copyright 2020 and onwards The Apache Software Foundation.

Apache Commons IO
Copyright 2002-2021 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (https://www.apache.org/).

Apache Commons Collections
Copyright 2001-2015 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

Apache Commons Logging
Copyright 2003-2013 The Apache Software Foundation

Apache Commons BeanUtils
Copyright 2000-2019 The Apache Software Foundation

Apache Commons Configuration
Copyright 2001-2017 The Apache Software Foundation

Apache Commons Lang
Copyright 2001-2021 The Apache Software Foundation

Apache Commons Text
Copyright 2014-2022 The Apache Software Foundation

Apache Commons Compress
Copyright 2002-2021 The Apache Software Foundation

---

The files in the package org.apache.commons.compress.archivers.sevenz
were derived from the LZMA SDK, version 9.20 (C/ and CPP/7zip/),
which has been placed in the public domain:

"LZMA SDK is placed in the public domain." (http://www.7-zip.org/sdk.html)

The test file lbzip2_32767.bz2 has been copied from libbzip2's source
repository:

This program, "bzip2", the associated library "libbzip2", and all
documentation, are copyright (C) 1996-2019 Julian R Seward.  All
rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

2. The origin of this software must not be misrepresented; you must 
   not claim that you wrote the original software.  If you use this 
   software in a product, an acknowledgment in the product 
   documentation would be appreciated but is not required.

3. Altered source versions must be plainly marked as such, and must
   not be misrepresented as being the original software.

4. The name of the author may not be used to endorse or promote 
   products derived from this software without specific prior written 
   permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Julian Seward, <EMAIL>

Kerby-kerb core
Copyright 2014-2017 The Apache Software Foundation

Kerby PKIX Project
Copyright 2014-2017 The Apache Software Foundation

Kerby ASN1 Project
Copyright 2014-2017 The Apache Software Foundation

Kerby Util
Copyright 2014-2017 The Apache Software Foundation

# Jackson JSON processor

Jackson is a high-performance, Free/Open Source JSON processing library.
It was originally written by Tatu Saloranta (<EMAIL>), and has
been in development since 2007.
It is currently developed by a community of developers.

## Licensing

Jackson 2.x core and extension components are licensed under Apache License 2.0
To find the details that apply to this artifact see the accompanying LICENSE file.

## Credits

A list of contributors may be found from CREDITS(-2.x) file, which is included
in some artifacts (usually source distributions); but is always available
from the source code management (SCM) system project uses.

Apache HttpClient
Copyright 1999-2020 The Apache Software Foundation

Apache HttpCore
Copyright 2005-2020 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Formats : Json
Copyright 2014-2023 The Apache Software Foundation

Flink : Format : Common
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-metrics-datadog
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0. (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.squareup.okhttp3:okhttp:3.14.9
- com.squareup.okio:okio:1.17.2

Note that publicsuffixes.gz is compiled from The Public Suffix List:
https://publicsuffix.org/list/public_suffix_list.dat

It is subject to the terms of the Mozilla Public License, v. 2.0:
https://mozilla.org/MPL/2.0/

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-metrics-graphite
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0. (http://www.apache.org/licenses/LICENSE-2.0.txt)

- io.dropwizard.metrics:metrics-core:3.2.6
- io.dropwizard.metrics:metrics-graphite:3.2.6


Flink : Metrics : Dropwizard
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-metrics-influxdb
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0. (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.squareup.moshi:moshi:1.8.0
- com.squareup.okhttp3:logging-interceptor:3.14.9
- com.squareup.okhttp3:okhttp:3.14.9
- com.squareup.okio:okio:1.17.2
- com.squareup.retrofit2:converter-moshi:2.6.2
- com.squareup.retrofit2:retrofit:2.6.2

This project bundles the following dependencies under the MIT license. (https://opensource.org/licenses/MIT)

- org.influxdb:influxdb-java:2.17

This project bundles a file called "publicsuffixes.gz":

Note that publicsuffixes.gz is compiled from The Public Suffix List:
https://publicsuffix.org/list/public_suffix_list.dat

It is subject to the terms of the Mozilla Public License, v. 2.0:
https://mozilla.org/MPL/2.0/

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Metrics : JMX
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-metrics-prometheus
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0. (http://www.apache.org/licenses/LICENSE-2.0.txt)

- io.prometheus:simpleclient:0.8.1
- io.prometheus:simpleclient_common:0.8.1
- io.prometheus:simpleclient_httpserver:0.8.1
- io.prometheus:simpleclient_pushgateway:0.8.1

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Metrics : Slf4j
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Metrics : StatsD
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-oss-fs-hadoop
Copyright 2014-2022 The Apache Software Foundation

This project includes software developed at
The Apache Software Foundation (http://www.apache.org/).

This project bundles the following dependencies under the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.aliyun.oss:aliyun-sdk-oss:3.13.2
- com.aliyun:aliyun-java-sdk-core:4.5.10
- com.aliyun:aliyun-java-sdk-kms:2.11.0
- com.aliyun:aliyun-java-sdk-ram:3.1.0
- com.google.code.gson:gson:2.8.6
- commons-codec:commons-codec:1.15
- commons-logging:commons-logging:1.1.3
- io.opentracing:opentracing-api:0.33.0
- io.opentracing:opentracing-noop:0.33.0
- io.opentracing:opentracing-util:0.33.0
- org.apache.hadoop:hadoop-aliyun:3.3.4
- org.apache.httpcomponents:httpclient:4.5.13
- org.apache.httpcomponents:httpcore:4.4.14
- org.codehaus.jettison:jettison:1.1
- org.ini4j:ini4j:0.5.4
- stax:stax-api:1.0.1

The binary distribution of this product bundles these dependencies under the Eclipse Public License - v 2.0 (https://www.eclipse.org/org/documents/epl-2.0/EPL-2.0.txt)
- org.jacoco:org.jacoco.agent:runtime:0.8.5

This project bundles the following dependencies under the JDOM license.
See bundled license files for details.

- org.jdom:jdom2:2.0.6


Flink : FileSystems : Hadoop FS
Copyright 2014-2023 The Apache Software Foundation

flink-fs-hadoop-shaded
Copyright 2014-2022 The Apache Software Foundation

- com.fasterxml.jackson.core:jackson-annotations:2.13.4
- com.fasterxml.jackson.core:jackson-core:2.13.4
- com.fasterxml.jackson.core:jackson-databind:********
- com.fasterxml.woodstox:woodstox-core:5.3.0
- com.google.guava:failureaccess:1.0
- com.google.guava:guava:27.0-jre
- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
- com.google.j2objc:j2objc-annotations:1.1
- commons-beanutils:commons-beanutils:1.9.4
- commons-collections:commons-collections:3.2.2
- commons-io:commons-io:2.11.0
- commons-logging:commons-logging:1.1.3
- org.apache.commons:commons-compress:1.21
- org.apache.commons:commons-configuration2:2.1.1
- org.apache.commons:commons-lang3:3.12.0
- org.apache.commons:commons-text:1.10.0
- org.apache.hadoop.thirdparty:hadoop-shaded-guava:1.1.1
- org.apache.hadoop.thirdparty:hadoop-shaded-protobuf_3_7:1.1.1
- org.apache.hadoop:hadoop-annotations:3.3.4
- org.apache.hadoop:hadoop-auth:3.3.4
- org.apache.hadoop:hadoop-common:3.3.4
- org.apache.kerby:kerb-core:1.0.1
- org.apache.kerby:kerby-asn1:1.0.1
- org.apache.kerby:kerby-pkix:1.0.1
- org.apache.kerby:kerby-util:1.0.1
- org.xerial.snappy:snappy-java:1.1.10.4

This project bundles the following dependencies under the MIT (https://opensource.org/licenses/MIT)

- org.checkerframework:checker-qual:2.5.2
- org.codehaus.mojo:animal-sniffer-annotations:1.17

This project bundles the following dependencies under BSD-2 License (https://opensource.org/licenses/BSD-2-Clause).
See bundled license files for details.

- dnsjava:dnsjava:2.1.7

This project bundles the following dependencies under the Go License (https://golang.org/LICENSE).
See bundled license files for details.

- com.google.re2j:re2j:1.1

This project bundles the following dependencies under BSD License (https://opensource.org/licenses/bsd-license.php).
See bundled license files for details.

- org.codehaus.woodstox:stax2-api:4.2.1 (https://github.com/FasterXML/stax2-api/tree/stax2-api-4.2.1)

Apache Hadoop
Copyright 2006 and onwards The Apache Software Foundation.

Export Control Notice
---------------------

This distribution includes cryptographic software.  The country in
which you currently reside may have restrictions on the import,
possession, use, and/or re-export to another country, of
encryption software.  BEFORE using any encryption software, please
check your country's laws, regulations and policies concerning the
import, possession, or use, and re-export of encryption software, to
see if this is permitted.  See <http://www.wassenaar.org/> for more
information.

The U.S. Government Department of Commerce, Bureau of Industry and
Security (BIS), has classified this software as Export Commodity
Control Number (ECCN) 5D002.C.1, which includes information security
software using or performing cryptographic functions with asymmetric
algorithms.  The form and manner of this Apache Software Foundation
distribution makes it eligible for export under the License Exception
ENC Technology Software Unrestricted (TSU) exception (see the BIS
Export Administration Regulations, Section 740.13) for both object
code and source code.

The following provides more details on the included cryptographic software:

This software uses the SSL libraries from the Jetty project written
by mortbay.org.
Hadoop Yarn Server Web Proxy uses the BouncyCastle Java
cryptography APIs written by the Legion of the Bouncy Castle Inc.

Apache Hadoop Third-party Libs
Copyright 2020 and onwards The Apache Software Foundation.

Apache Commons IO
Copyright 2002-2021 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (https://www.apache.org/).

Apache Commons Collections
Copyright 2001-2015 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

Apache Commons Logging
Copyright 2003-2013 The Apache Software Foundation

Apache Commons BeanUtils
Copyright 2000-2019 The Apache Software Foundation

Apache Commons Configuration
Copyright 2001-2017 The Apache Software Foundation

Apache Commons Lang
Copyright 2001-2021 The Apache Software Foundation

Apache Commons Text
Copyright 2014-2022 The Apache Software Foundation

Apache Commons Compress
Copyright 2002-2021 The Apache Software Foundation

---

The files in the package org.apache.commons.compress.archivers.sevenz
were derived from the LZMA SDK, version 9.20 (C/ and CPP/7zip/),
which has been placed in the public domain:

"LZMA SDK is placed in the public domain." (http://www.7-zip.org/sdk.html)

The test file lbzip2_32767.bz2 has been copied from libbzip2's source
repository:

This program, "bzip2", the associated library "libbzip2", and all
documentation, are copyright (C) 1996-2019 Julian R Seward.  All
rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

2. The origin of this software must not be misrepresented; you must 
   not claim that you wrote the original software.  If you use this 
   software in a product, an acknowledgment in the product 
   documentation would be appreciated but is not required.

3. Altered source versions must be plainly marked as such, and must
   not be misrepresented as being the original software.

4. The name of the author may not be used to endorse or promote 
   products derived from this software without specific prior written 
   permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Julian Seward, <EMAIL>

Kerby-kerb core
Copyright 2014-2017 The Apache Software Foundation

Kerby PKIX Project
Copyright 2014-2017 The Apache Software Foundation

Kerby ASN1 Project
Copyright 2014-2017 The Apache Software Foundation

Kerby Util
Copyright 2014-2017 The Apache Software Foundation

# Jackson JSON processor

Jackson is a high-performance, Free/Open Source JSON processing library.
It was originally written by Tatu Saloranta (<EMAIL>), and has
been in development since 2007.
It is currently developed by a community of developers.

## Licensing

Jackson 2.x core and extension components are licensed under Apache License 2.0
To find the details that apply to this artifact see the accompanying LICENSE file.

## Credits

A list of contributors may be found from CREDITS(-2.x) file, which is included
in some artifacts (usually source distributions); but is always available
from the source code management (SCM) system project uses.

Apache HttpClient
Copyright 1999-2020 The Apache Software Foundation

Apache HttpCore
Copyright 2005-2020 The Apache Software Foundation

Apache Commons Codec
Copyright 2002-2020 The Apache Software Foundation

src/test/org/apache/commons/codec/language/DoubleMetaphoneTest.java
contains test data from http://aspell.net/test/orig/batch0.tab.
Copyright (C) 2002 Kevin Atkinson (<EMAIL>)

===============================================================================

The content of package org.apache.commons.codec.language.bm has been translated
from the original php source code available at http://stevemorse.org/phoneticinfo.htm
with permission from the original authors.
Original source copyright:
Copyright (c) 2008 Alexander Beider & Stephen P. Morse.

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-python
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.fasterxml.jackson.core:jackson-annotations:2.13.4
- com.fasterxml.jackson.core:jackson-core:2.13.4
- com.fasterxml.jackson.core:jackson-databind:********
- com.google.flatbuffers:flatbuffers-java:1.12.0
- io.netty:netty-buffer:4.1.70.Final
- io.netty:netty-common:4.1.70.Final
- joda-time:joda-time:2.5
- org.apache.arrow:arrow-format:5.0.0
- org.apache.arrow:arrow-memory-core:5.0.0
- org.apache.arrow:arrow-memory-netty:5.0.0
- org.apache.arrow:arrow-vector:5.0.0
- org.apache.beam:beam-model-fn-execution:2.43.0
- org.apache.beam:beam-model-job-management:2.43.0
- org.apache.beam:beam-model-pipeline:2.43.0
- org.apache.beam:beam-runners-core-construction-java:2.43.0
- org.apache.beam:beam-runners-core-java:2.43.0
- org.apache.beam:beam-runners-java-fn-execution:2.43.0
- org.apache.beam:beam-sdks-java-core:2.43.0
- org.apache.beam:beam-sdks-java-fn-execution:2.43.0
- org.apache.beam:beam-vendor-guava-26_0-jre:0.1
- org.apache.beam:beam-vendor-grpc-1_48_1:0.1
- com.alibaba:pemja:0.3.0

This project bundles the following dependencies under the BSD license.
See bundled license files for details

- net.sf.py4j:py4j:********
- com.google.protobuf:protobuf-java:3.21.7

This project bundles the following dependencies under the MIT license. (https://opensource.org/licenses/MIT)
See bundled license files for details.

- net.razorvine:pyrolite:4.13

The bundled Apache Beam dependencies bundle the following dependencies under the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.google.api.grpc:proto-google-common-protos:2.9.0
- com.google.code.gson:gson:2.9.0
- com.google.guava:guava:31.1-jre
- io.grpc:grpc-auth:1.48.1
- io.grpc:grpc-core:1.48.1
- io.grpc:grpc-context:1.48.1
- io.grpc:grpc-netty:1.48.1
- io.grpc:grpc-protobuf:1.48.1
- io.grpc:grpc-stub:1.48.1
- io.grpc:grpc-testing:1.48.1
- io.netty:netty-buffer:4.1.77.Final
- io.netty:netty-codec:4.1.77.Final
- io.netty:netty-codec-http:4.1.77.Final
- io.netty:netty-codec-http2:4.1.77.Final
- io.netty:netty-codec-socks:4.1.77.Final
- io.netty:netty-common:4.1.77.Final
- io.netty:netty-handler:4.1.77.Final
- io.netty:netty-handler-proxy:4.1.77.Final
- io.netty:netty-resolver:4.1.77.Final
- io.netty:netty-transport:4.1.77.Final
- io.netty:netty-transport-native-epoll:4.1.77.Final:linux-x86_64
- io.netty:netty-transport-native-unix-common:4.1.77.Final
- io.netty:netty-tcnative-boringssl-static:2.0.53.Final
- io.opencensus:opencensus-api:0.31.0
- io.opencensus:opencensus-contrib-grpc-metrics:0.31.0
- io.perfmark:perfmark-api:0.25.0

The bundled Apache Beam dependencies bundle the following dependencies under the BSD license.
See bundled license files for details

- com.google.auth:google-auth-library-credentials:1.4.0
- com.google.protobuf:protobuf-java:3.21.1
- com.google.protobuf:protobuf-java-util:3.21.1

Apache Beam
Copyright 2016-2018 The Apache Software Foundation

Based on source code originally developed by
Google (http://www.google.com/).

This product includes software developed at
Google (http://www.google.com/).

Apache Commons Compress
Copyright 2002-2021 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (https://www.apache.org/).

---

The files in the package org.apache.commons.compress.archivers.sevenz
were derived from the LZMA SDK, version 9.20 (C/ and CPP/7zip/),
which has been placed in the public domain:

"LZMA SDK is placed in the public domain." (http://www.7-zip.org/sdk.html)

The test file lbzip2_32767.bz2 has been copied from libbzip2's source
repository:

This program, "bzip2", the associated library "libbzip2", and all
documentation, are copyright (C) 1996-2019 Julian R Seward.  All
rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

2. The origin of this software must not be misrepresented; you must 
   not claim that you wrote the original software.  If you use this 
   software in a product, an acknowledgment in the product 
   documentation would be appreciated but is not required.

3. Altered source versions must be plainly marked as such, and must
   not be misrepresented as being the original software.

4. The name of the author may not be used to endorse or promote 
   products derived from this software without specific prior written 
   permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Julian Seward, <EMAIL>

=============================================================================
= NOTICE file corresponding to section 4d of the Apache License Version 2.0 =
=============================================================================
This product includes software developed by
Joda.org (http://www.joda.org/).


Arrow Vectors
Copyright 2021 The Apache Software Foundation

Arrow Format
Copyright 2021 The Apache Software Foundation

Arrow Memory - Core
Copyright 2021 The Apache Software Foundation

# Jackson JSON processor

Jackson is a high-performance, Free/Open Source JSON processing library.
It was originally written by Tatu Saloranta (<EMAIL>), and has
been in development since 2007.
It is currently developed by a community of developers.

## Licensing

Jackson 2.x core and extension components are licensed under Apache License 2.0
To find the details that apply to this artifact see the accompanying LICENSE file.

## Credits

A list of contributors may be found from CREDITS(-2.x) file, which is included
in some artifacts (usually source distributions); but is always available
from the source code management (SCM) system project uses.

Arrow Memory - Netty
Copyright 2021 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Queryable state : Runtime
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-s3-fs-hadoop
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.amazonaws:aws-java-sdk-core:1.12.319
- com.amazonaws:aws-java-sdk-dynamodb:1.12.319
- com.amazonaws:aws-java-sdk-kms:1.12.319
- com.amazonaws:aws-java-sdk-s3:1.12.319
- com.amazonaws:aws-java-sdk-sts:1.12.319
- com.amazonaws:jmespath-java:1.12.319
- com.fasterxml.jackson.core:jackson-annotations:2.13.4
- com.fasterxml.jackson.core:jackson-core:2.13.4
- com.fasterxml.jackson.core:jackson-databind:********
- com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.13.4
- com.fasterxml.woodstox:woodstox-core:5.3.0
- com.google.guava:failureaccess:1.0
- com.google.guava:guava:27.0-jre
- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
- com.google.j2objc:j2objc-annotations:1.1
- commons-beanutils:commons-beanutils:1.9.4
- commons-codec:commons-codec:1.15
- commons-collections:commons-collections:3.2.2
- commons-io:commons-io:2.11.0
- commons-logging:commons-logging:1.1.3
- joda-time:joda-time:2.5
- org.apache.commons:commons-compress:1.21
- org.apache.commons:commons-configuration2:2.1.1
- org.apache.commons:commons-lang3:3.12.0
- org.apache.commons:commons-text:1.10.0
- org.apache.hadoop.thirdparty:hadoop-shaded-guava:1.1.1
- org.apache.hadoop.thirdparty:hadoop-shaded-protobuf_3_7:1.1.1
- org.apache.hadoop:hadoop-annotations:3.3.4
- org.apache.hadoop:hadoop-auth:3.3.4
- org.apache.hadoop:hadoop-aws:3.3.4
- org.apache.hadoop:hadoop-common:3.3.4
- org.apache.httpcomponents:httpclient:4.5.13
- org.apache.httpcomponents:httpcore:4.4.14
- org.apache.kerby:kerb-core:1.0.1
- org.apache.kerby:kerby-asn1:1.0.1
- org.apache.kerby:kerby-pkix:1.0.1
- org.apache.kerby:kerby-util:1.0.1
- org.wildfly.openssl:wildfly-openssl:1.0.7.Final
- org.xerial.snappy:snappy-java:1.1.10.4
- software.amazon.ion:ion-java:1.0.2

This project bundles the following dependencies under BSD-2 License (https://opensource.org/licenses/BSD-2-Clause).
See bundled license files for details.

- dnsjava:dnsjava:2.1.7

This project bundles the following dependencies under the MIT (https://opensource.org/licenses/MIT)

- org.checkerframework:checker-qual:2.5.2
- org.codehaus.mojo:animal-sniffer-annotations:1.17

This project bundles the following dependencies under the CDDL 1.1 license.
See bundled license files for details.

- javax.xml.bind:jaxb-api:2.3.1

This project bundles the following dependencies under the Go License (https://golang.org/LICENSE).
See bundled license files for details.

- com.google.re2j:re2j:1.1

This project bundles the following dependencies under BSD License (https://opensource.org/licenses/bsd-license.php).
See bundled license files for details.

- org.codehaus.woodstox:stax2-api:4.2.1 (https://github.com/FasterXML/stax2-api/tree/stax2-api-4.2.1)

The bundled Apache Hadoop Relocated (Shaded) Third-party Miscellaneous Libs
org.apache.hadoop.thirdparty:hadoop-shaded-guava dependency bundles the following dependencies under
the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.google.guava:guava:30.1.1-jre

The bundled Apache Hadoop Relocated (Shaded) Third-party Miscellaneous Libs
org.apache.hadoop.thirdparty:hadoop-shaded-protobuf_3_7 dependency bundles the following dependencies under
the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.google.protobuf:protobuf-java:3.7.1

Apache Commons Lang
Copyright 2001-2021 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (https://www.apache.org/).

Apache Commons Text
Copyright 2014-2022 The Apache Software Foundation

Apache Commons Collections
Copyright 2001-2015 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

Apache Commons Compress
Copyright 2002-2021 The Apache Software Foundation

---

The files in the package org.apache.commons.compress.archivers.sevenz
were derived from the LZMA SDK, version 9.20 (C/ and CPP/7zip/),
which has been placed in the public domain:

"LZMA SDK is placed in the public domain." (http://www.7-zip.org/sdk.html)

The test file lbzip2_32767.bz2 has been copied from libbzip2's source
repository:

This program, "bzip2", the associated library "libbzip2", and all
documentation, are copyright (C) 1996-2019 Julian R Seward.  All
rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

2. The origin of this software must not be misrepresented; you must 
   not claim that you wrote the original software.  If you use this 
   software in a product, an acknowledgment in the product 
   documentation would be appreciated but is not required.

3. Altered source versions must be plainly marked as such, and must
   not be misrepresented as being the original software.

4. The name of the author may not be used to endorse or promote 
   products derived from this software without specific prior written 
   permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Julian Seward, <EMAIL>


Flink : FileSystems : S3 FS Base
Copyright 2014-2023 The Apache Software Foundation

Apache Hadoop
Copyright 2006 and onwards The Apache Software Foundation.

Export Control Notice
---------------------

This distribution includes cryptographic software.  The country in
which you currently reside may have restrictions on the import,
possession, use, and/or re-export to another country, of
encryption software.  BEFORE using any encryption software, please
check your country's laws, regulations and policies concerning the
import, possession, or use, and re-export of encryption software, to
see if this is permitted.  See <http://www.wassenaar.org/> for more
information.

The U.S. Government Department of Commerce, Bureau of Industry and
Security (BIS), has classified this software as Export Commodity
Control Number (ECCN) 5D002.C.1, which includes information security
software using or performing cryptographic functions with asymmetric
algorithms.  The form and manner of this Apache Software Foundation
distribution makes it eligible for export under the License Exception
ENC Technology Software Unrestricted (TSU) exception (see the BIS
Export Administration Regulations, Section 740.13) for both object
code and source code.

The following provides more details on the included cryptographic software:

This software uses the SSL libraries from the Jetty project written
by mortbay.org.
Hadoop Yarn Server Web Proxy uses the BouncyCastle Java
cryptography APIs written by the Legion of the Bouncy Castle Inc.

Apache Hadoop Third-party Libs
Copyright 2020 and onwards The Apache Software Foundation.

Apache Commons Logging
Copyright 2003-2013 The Apache Software Foundation

Apache Commons BeanUtils
Copyright 2000-2019 The Apache Software Foundation

Apache Commons Configuration
Copyright 2001-2017 The Apache Software Foundation

Kerby-kerb core
Copyright 2014-2017 The Apache Software Foundation

Kerby PKIX Project
Copyright 2014-2017 The Apache Software Foundation

Kerby ASN1 Project
Copyright 2014-2017 The Apache Software Foundation

Kerby Util
Copyright 2014-2017 The Apache Software Foundation

# Jackson JSON processor

Jackson is a high-performance, Free/Open Source JSON processing library.
It was originally written by Tatu Saloranta (<EMAIL>), and has
been in development since 2007.
It is currently developed by a community of developers.

## Licensing

Jackson 2.x core and extension components are licensed under Apache License 2.0
To find the details that apply to this artifact see the accompanying LICENSE file.

## Credits

A list of contributors may be found from CREDITS(-2.x) file, which is included
in some artifacts (usually source distributions); but is always available
from the source code management (SCM) system project uses.

Flink : FileSystems : Hadoop FS
Copyright 2014-2023 The Apache Software Foundation

Apache Commons Codec
Copyright 2002-2020 The Apache Software Foundation

src/test/org/apache/commons/codec/language/DoubleMetaphoneTest.java
contains test data from http://aspell.net/test/orig/batch0.tab.
Copyright (C) 2002 Kevin Atkinson (<EMAIL>)

===============================================================================

The content of package org.apache.commons.codec.language.bm has been translated
from the original php source code available at http://stevemorse.org/phoneticinfo.htm
with permission from the original authors.
Original source copyright:
Copyright (c) 2008 Alexander Beider & Stephen P. Morse.

Apache HttpClient
Copyright 1999-2020 The Apache Software Foundation

Apache HttpCore
Copyright 2005-2020 The Apache Software Foundation

=============================================================================
= NOTICE file corresponding to section 4d of the Apache License Version 2.0 =
=============================================================================
This product includes software developed by
Joda.org (http://www.joda.org/).

Apache Commons IO
Copyright 2002-2021 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-s3-fs-presto
Copyright 2014-2022 The Apache Software Foundation

This project includes software developed at
The Apache Software Foundation (http://www.apache.org/).

This project bundles the following dependencies under the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.amazonaws:aws-java-sdk-core:1.12.319
- com.amazonaws:aws-java-sdk-dynamodb:1.12.319
- com.amazonaws:aws-java-sdk-kms:1.12.319
- com.amazonaws:aws-java-sdk-s3:1.12.319
- com.amazonaws:aws-java-sdk-sts:1.12.319
- com.amazonaws:jmespath-java:1.12.319
- com.facebook.airlift:configuration:0.201
- com.facebook.airlift:log:0.201
- com.facebook.airlift:stats:0.201
- com.facebook.presto.hadoop:hadoop-apache2:2.7.4-9
- com.facebook.presto:presto-common:0.272
- com.facebook.presto:presto-hive-common:0.272
- com.facebook.presto:presto-hive-metastore:0.272
- com.facebook.presto:presto-hive:0.272
- com.fasterxml.jackson.core:jackson-annotations:2.13.4
- com.fasterxml.jackson.core:jackson-core:2.13.4
- com.fasterxml.jackson.core:jackson-databind:********
- com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.13.4
- com.fasterxml.woodstox:woodstox-core:5.3.0
- com.google.guava:guava:26.0-jre
- com.google.inject:guice:4.2.2
- commons-beanutils:commons-beanutils:1.9.4
- commons-codec:commons-codec:1.15
- commons-collections:commons-collections:3.2.2
- commons-io:commons-io:2.11.0
- commons-logging:commons-logging:1.1.3
- io.airlift:slice:0.38
- io.airlift:units:1.3
- joda-time:joda-time:2.5
- org.alluxio:alluxio-shaded-client:2.7.3
- org.apache.commons:commons-compress:1.21
- org.apache.commons:commons-configuration2:2.1.1
- org.apache.commons:commons-lang3:3.12.0
- org.apache.commons:commons-text:1.10.0
- org.apache.hadoop.thirdparty:hadoop-shaded-guava:1.1.1
- org.apache.hadoop.thirdparty:hadoop-shaded-protobuf_3_7:1.1.1
- org.apache.hadoop:hadoop-annotations:3.3.4
- org.apache.hadoop:hadoop-auth:3.3.4
- org.apache.hadoop:hadoop-aws:3.3.4
- org.apache.hadoop:hadoop-common:3.3.4
- org.apache.httpcomponents:httpclient:4.5.13
- org.apache.httpcomponents:httpcore:4.4.14
- org.apache.hudi:hudi-presto-bundle:0.10.1
- org.apache.kerby:kerb-core:1.0.1
- org.apache.kerby:kerby-asn1:1.0.1
- org.apache.kerby:kerby-pkix:1.0.1
- org.apache.kerby:kerby-util:1.0.1
- org.weakref:jmxutils:1.19
- org.wildfly.openssl:wildfly-openssl:1.0.7.Final
- org.xerial.snappy:snappy-java:1.1.10.4
- software.amazon.ion:ion-java:1.0.2

This project bundles the following dependencies under BSD-2 License (https://opensource.org/licenses/BSD-2-Clause).
See bundled license files for details.

- dnsjava:dnsjava:2.1.7

This project bundles the following dependencies under the Creative Commons CC0 1.0 Universal Public Domain Dedication License (http://creativecommons.org/publicdomain/zero/1.0/)
See bundled license files for details.

- org.hdrhistogram:HdrHistogram:2.1.9

This project bundles the following dependencies under the CDDL 1.1 license.
See bundled license files for details.

- javax.xml.bind:jaxb-api:2.3.1

This project bundles the following dependencies under the Go License (https://golang.org/LICENSE).
See bundled license files for details.

- com.google.re2j:re2j:1.1

This project bundles the following dependencies under BSD License (https://opensource.org/licenses/bsd-license.php).
See bundled license files for details.

- org.codehaus.woodstox:stax2-api:4.2.1 (https://github.com/FasterXML/stax2-api/tree/stax2-api-4.2.1)

This project bundles the following dependencies under the Public Domain.
See bundled license files for details.

- aopalliance:aopalliance:1.0

The bundled Apache Hadoop Relocated (Shaded) Third-party Miscellaneous Libs
org.apache.hadoop.thirdparty:hadoop-shaded-guava dependency bundles the following dependencies under
the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.google.guava:guava:30.1.1-jre

The bundled Apache Hadoop Relocated (Shaded) Third-party Miscellaneous Libs
org.apache.hadoop.thirdparty:hadoop-shaded-protobuf_3_7 dependency bundles the following dependencies under
the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.google.protobuf:protobuf-java:3.7.1

Apache Commons Lang
Copyright 2001-2021 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (https://www.apache.org/).

Apache Commons Text
Copyright 2014-2022 The Apache Software Foundation

Apache Commons Collections
Copyright 2001-2015 The Apache Software Foundation

This product includes software developed by
The Apache Software Foundation (http://www.apache.org/).

Apache Commons Compress
Copyright 2002-2021 The Apache Software Foundation

---

The files in the package org.apache.commons.compress.archivers.sevenz
were derived from the LZMA SDK, version 9.20 (C/ and CPP/7zip/),
which has been placed in the public domain:

"LZMA SDK is placed in the public domain." (http://www.7-zip.org/sdk.html)

The test file lbzip2_32767.bz2 has been copied from libbzip2's source
repository:

This program, "bzip2", the associated library "libbzip2", and all
documentation, are copyright (C) 1996-2019 Julian R Seward.  All
rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

2. The origin of this software must not be misrepresented; you must 
   not claim that you wrote the original software.  If you use this 
   software in a product, an acknowledgment in the product 
   documentation would be appreciated but is not required.

3. Altered source versions must be plainly marked as such, and must
   not be misrepresented as being the original software.

4. The name of the author may not be used to endorse or promote 
   products derived from this software without specific prior written 
   permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Julian Seward, <EMAIL>


Flink : FileSystems : Hadoop FS
Copyright 2014-2023 The Apache Software Foundation

Apache Commons IO
Copyright 2002-2021 The Apache Software Foundation

Apache Hadoop
Copyright 2006 and onwards The Apache Software Foundation.

Export Control Notice
---------------------

This distribution includes cryptographic software.  The country in
which you currently reside may have restrictions on the import,
possession, use, and/or re-export to another country, of
encryption software.  BEFORE using any encryption software, please
check your country's laws, regulations and policies concerning the
import, possession, or use, and re-export of encryption software, to
see if this is permitted.  See <http://www.wassenaar.org/> for more
information.

The U.S. Government Department of Commerce, Bureau of Industry and
Security (BIS), has classified this software as Export Commodity
Control Number (ECCN) 5D002.C.1, which includes information security
software using or performing cryptographic functions with asymmetric
algorithms.  The form and manner of this Apache Software Foundation
distribution makes it eligible for export under the License Exception
ENC Technology Software Unrestricted (TSU) exception (see the BIS
Export Administration Regulations, Section 740.13) for both object
code and source code.

The following provides more details on the included cryptographic software:

This software uses the SSL libraries from the Jetty project written
by mortbay.org.
Hadoop Yarn Server Web Proxy uses the BouncyCastle Java
cryptography APIs written by the Legion of the Bouncy Castle Inc.

Apache Hadoop Third-party Libs
Copyright 2020 and onwards The Apache Software Foundation.

Apache Commons Logging
Copyright 2003-2013 The Apache Software Foundation

Apache Commons BeanUtils
Copyright 2000-2019 The Apache Software Foundation

Apache Commons Configuration
Copyright 2001-2017 The Apache Software Foundation

Kerby-kerb core
Copyright 2014-2017 The Apache Software Foundation

Kerby PKIX Project
Copyright 2014-2017 The Apache Software Foundation

Kerby ASN1 Project
Copyright 2014-2017 The Apache Software Foundation

Kerby Util
Copyright 2014-2017 The Apache Software Foundation

Apache Commons Codec
Copyright 2002-2020 The Apache Software Foundation

src/test/org/apache/commons/codec/language/DoubleMetaphoneTest.java
contains test data from http://aspell.net/test/orig/batch0.tab.
Copyright (C) 2002 Kevin Atkinson (<EMAIL>)

===============================================================================

The content of package org.apache.commons.codec.language.bm has been translated
from the original php source code available at http://stevemorse.org/phoneticinfo.htm
with permission from the original authors.
Original source copyright:
Copyright (c) 2008 Alexander Beider & Stephen P. Morse.

Apache HttpClient
Copyright 1999-2020 The Apache Software Foundation

Apache HttpCore
Copyright 2005-2020 The Apache Software Foundation

Flink : FileSystems : S3 FS Base
Copyright 2014-2023 The Apache Software Foundation

Apache Commons Lang
Copyright 2001-2011 The Apache Software Foundation

hudi-presto-bundle
Copyright 2022 The Apache Software Foundation

hudi-common
Copyright 2022 The Apache Software Foundation

Objenesis
Copyright 2006-2017 Joe Walnes, Henri Tremblay, Leonardo Mesquita

Apache HBase - Server
Copyright 2007-2016 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).
====
Apache HBase - Server contained works

This product contains additional works that are distributed under licenses
other than ASL v2. See LICENSE for full details

--
Our Orca logo we got here: http://www.vectorfree.com/jumping-orca
It is (c) 2012 by Vector Free user OverSurge

It is licensed Creative Commons Attribution 3.0.

We changed the logo by stripping the colored background, inverting
it and then rotating it some.

Later we found that vectorfree.com image is not properly licensed.
The original is owned by vectorportal.com. The original was
relicensed so we could use it as Creative Commons Attribution 3.0.
The license is bundled with the download available here:
http://www.vectorportal.com/subcategory/205/KILLER-WHALE-FREE-VECTOR.eps/ifile/9136/detailtest.asp
--
This product includes portions of the Bootstrap project v3.0.0

Copyright 2013 Twitter, Inc.

Licensed under the Apache License v2.0

This product uses the Glyphicons Halflings icon set.

http://glyphicons.com/

Copyright Jan Kovařík

Licensed under the Apache License v2.0 as a part of the Bootstrap project.

Apache HBase - Protocol
Copyright 2007-2016 The Apache Software Foundation

Apache HBase - Common
Copyright 2007-2016 The Apache Software Foundation

Apache HBase - Client
Copyright 2007-2016 The Apache Software Foundation

hudi-hadoop-mr
Copyright 2022 The Apache Software Foundation

htrace-core
Copyright 2015 The Apache Software Foundation

--
This product includes portions of the Guava project v14, specifically
'hbase-common/src/main/java/org/apache/hadoop/hbase/io/LimitInputStream.java'

Copyright (C) 2007 The Guava Authors

Licensed under the Apache License, Version 2.0

Apache HBase - Annotations
Copyright 2007-2016 The Apache Software Foundation

Alluxio
Copyright 2016 Alluxio Open Foundation

==========

Alluxio project contains subcomponents with separate copyright notices and license terms.

Your use of the source code for the these subcomponents is subject to the terms and conditions of
their respective licenses.

See the LICENSE file for a list of subcomponents and dependencies and their respective licenses.

----------

This product depends on various Apache Commons libraries which are subject to the
following NOTICEs:

This product includes software from the Spring Framework,
under the Apache License 2.0 (see: StringUtils.containsWhitespace())

This product includes software developed for Orekit by
CS Systèmes d'Information (http://www.c-s.fr/)
Copyright 2010-2012 CS Systèmes d'Information

EL-8 patch - Copyright 2004-2007 Jamie Taylor
http://issues.apache.org/jira/browse/EL-8

This products depends on Netty which is subject to the following NOTICEs:

This product contains the extensions to Java Collections Framework which has
been derived from the works by JSR-166 EG, Doug Lea, and Jason T. Greene:

  * LICENSE:
    * Public Domain
  * HOMEPAGE:
    * http://gee.cs.oswego.edu/cgi-bin/viewcvs.cgi/jsr166/
    * http://viewvc.jboss.org/cgi-bin/viewvc.cgi/jbosscache/experimental/jsr166/

This product contains a modified version of Robert Harder's Public Domain
Base64 Encoder and Decoder, which can be obtained at:

  * LICENSE:
    * Public Domain
  * HOMEPAGE:
    * http://iharder.sourceforge.net/current/java/base64/

This product contains a modified portion of 'Webbit', an event based
WebSocket and HTTP server, which can be obtained at:

  * LICENSE:
    * BSD License
  * HOMEPAGE:
    * https://github.com/joewalnes/webbit

This product contains a modified portion of 'SLF4J', a simple logging
facade for Java, which can be obtained at:

  * LICENSE:
    * MIT License
  * HOMEPAGE:
    * http://www.slf4j.org/

This product contains a modified portion of 'ArrayDeque', written by Josh
Bloch of Google, Inc:

  * LICENSE:
    * Public Domain

This product contains a modified portion of 'Apache Harmony', an open source
Java SE, which can be obtained at:

  * LICENSE:
    * Apache License 2.0
  * HOMEPAGE:
    * http://archive.apache.org/dist/harmony/

This product contains a modified portion of 'jbzip2', a Java bzip2 compression
and decompression library written by Matthew J. Francis. It can be obtained at:

  * LICENSE:
    * MIT License
  * HOMEPAGE:
    * https://code.google.com/p/jbzip2/

This product contains a modified portion of 'libdivsufsort', a C API library to construct
the suffix array and the Burrows-Wheeler transformed string for any input string of
a constant-size alphabet written by Yuta Mori. It can be obtained at:

  * LICENSE:
    * MIT License
  * HOMEPAGE:
    * https://code.google.com/p/libdivsufsort/

This product optionally depends on 'JZlib', a re-implementation of zlib in
pure Java, which can be obtained at:

  * LICENSE:
    * BSD style License
  * HOMEPAGE:
    * http://www.jcraft.com/jzlib/

This product contains a modified portion of 'jfastlz', a Java port of FastLZ compression
and decompression library written by William Kinney. It can be obtained at:

  * LICENSE:
    * MIT License
  * HOMEPAGE:
    * https://code.google.com/p/jfastlz/

This product optionally depends on 'Snappy', a compression library produced
by Google Inc, which can be obtained at:

  * LICENSE:
    * New BSD License
  * HOMEPAGE:
    * http://code.google.com/p/snappy/

This product optionally depends on 'jnr-fuse', a FUSE implementation in java using
Java Native Runtime (JNR), written by Sergey Tselovalnikov. It can be obtained at:

  * LICENSE:
    * MIT License
  * HOMEPAGE:
    * https://github.com/SerCeMan/jnr-fuse

Google Guice - Core Library
Copyright 2006-2018 Google, Inc.

=============================================================================
= NOTICE file corresponding to section 4d of the Apache License Version 2.0 =
=============================================================================
This product includes software developed by
Joda.org (http://www.joda.org/).

# Jackson JSON processor

Jackson is a high-performance, Free/Open Source JSON processing library.
It was originally written by Tatu Saloranta (<EMAIL>), and has
been in development since 2007.
It is currently developed by a community of developers.

## Licensing

Jackson 2.x core and extension components are licensed under Apache License 2.0
To find the details that apply to this artifact see the accompanying LICENSE file.

## Credits

A list of contributors may be found from CREDITS(-2.x) file, which is included
in some artifacts (usually source distributions); but is always available
from the source code management (SCM) system project uses.

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-dist-scala
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.twitter:chill_2.12:0.7.6

The following dependencies all share the same BSD license which you find under licenses/LICENSE.scala.

- org.scala-lang:scala-compiler:2.12.7
- org.scala-lang:scala-library:2.12.7
- org.scala-lang:scala-reflect:2.12.7
- org.scala-lang.modules:scala-xml_2.12:1.0.6


Flink : Scala
Copyright 2014-2023 The Apache Software Foundation

Flink : Streaming Scala
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink-shaded
// ------------------------------------------------------------------

Apache Flink-shaded
Copyright 2006-2022 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-shaded-netty-openssl-static
Copyright 2014-2021 The Apache Software Foundation

This project includes software developed at
The Apache Software Foundation (http://www.apache.org/).

This project bundles the following dependencies under the Apache Software License 2.0 (http://www.apache.org/licenses/LICENSE-2.0.txt)

- io.netty:netty-tcnative:2.0.54.Final
- io.netty:netty-tcnative-classes:2.0.54.Final

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-sql-client
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the BSD license.
See bundled license files for details.

- org.jline:jline-terminal:3.21.0
- org.jline:jline-reader:3.21.0

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Table : SQL Gateway
Copyright 2014-2023 The Apache Software Foundation

Flink : Table : SQL Gateway : API
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Libraries : State processor API
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-table-api-java-uber
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the ICU license.
See bundled license files for details

- com.ibm.icu:icu4j:67.1


Flink : Table : Common
Copyright 2014-2023 The Apache Software Foundation

Flink : Table : API Java
Copyright 2014-2023 The Apache Software Foundation

Flink : Table : API bridge base
Copyright 2014-2023 The Apache Software Foundation

Flink : Table : API Java bridge
Copyright 2014-2023 The Apache Software Foundation

Flink : Table : SQL Gateway : API
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Flink : Table : Planner Loader
Copyright 2014-2023 The Apache Software Foundation

flink-table-planner-loader-bundle
Copyright 2014-2022 The Apache Software Foundation

The following dependencies all share the same BSD license which you find under licenses/LICENSE.scala.

- org.scala-lang:scala-compiler:2.12.7
- org.scala-lang:scala-library:2.12.7
- org.scala-lang:scala-reflect:2.12.7

flink-table-planner
Copyright 2014-2023 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0. (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.jayway.jsonpath:json-path:2.6.0
- com.google.guava:guava:29.0-jre
- com.google.guava:failureaccess:1.0.1
- com.esri.geometry:esri-geometry-api:2.2.0
- org.apache.calcite:calcite-core:1.29.0
- org.apache.calcite:calcite-linq4j:1.29.0
- org.apache.calcite.avatica:avatica-core:1.20.0
- commons-codec:commons-codec:1.15
- commons-io:commons-io:2.11.0

This project bundles the following dependencies under the MIT License. (http://www.opensource.org/licenses/mit-license.php)

- org.checkerframework:checker-qual:3.10.0

Flink : Table : SQL Parser
Copyright 2014-2023 The Apache Software Foundation

Flink : Table : SQL Parser Hive
Copyright 2014-2023 The Apache Software Foundation

Apache Calcite
Copyright 2012-2021 The Apache Software Foundation

This product is based on source code originally developed
by DynamoBI Corporation, LucidEra Inc., SQLstream Inc. and others
under the auspices of the Eigenbase Foundation
and released as the LucidDB project.

The web site includes files generated by Jekyll.

Apache Calcite -- Avatica
Copyright 2012-2021 The Apache Software Foundation

Apache Commons Codec
Copyright 2002-2020 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (https://www.apache.org/).

src/test/org/apache/commons/codec/language/DoubleMetaphoneTest.java
contains test data from http://aspell.net/test/orig/batch0.tab.
Copyright (C) 2002 Kevin Atkinson (<EMAIL>)

===============================================================================

The content of package org.apache.commons.codec.language.bm has been translated
from the original php source code available at http://stevemorse.org/phoneticinfo.htm
with permission from the original authors.
Original source copyright:
Copyright (c) 2008 Alexander Beider & Stephen P. Morse.

Apache Commons IO
Copyright 2002-2021 The Apache Software Foundation

Flink : Scala
Copyright 2014-2023 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-table-planner
Copyright 2014-2023 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0. (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.jayway.jsonpath:json-path:2.6.0
- com.google.guava:guava:29.0-jre
- com.google.guava:failureaccess:1.0.1
- com.esri.geometry:esri-geometry-api:2.2.0
- org.apache.calcite:calcite-core:1.29.0
- org.apache.calcite:calcite-linq4j:1.29.0
- org.apache.calcite.avatica:avatica-core:1.20.0
- commons-codec:commons-codec:1.15
- commons-io:commons-io:2.11.0

This project bundles the following dependencies under the MIT License. (http://www.opensource.org/licenses/mit-license.php)

- org.checkerframework:checker-qual:3.10.0


Flink : Table : SQL Parser
Copyright 2014-2023 The Apache Software Foundation

Flink : Table : SQL Parser Hive
Copyright 2014-2023 The Apache Software Foundation

Apache Calcite
Copyright 2012-2021 The Apache Software Foundation

This product is based on source code originally developed
by DynamoBI Corporation, LucidEra Inc., SQLstream Inc. and others
under the auspices of the Eigenbase Foundation
and released as the LucidDB project.

The web site includes files generated by Jekyll.

Apache Calcite -- Avatica
Copyright 2012-2021 The Apache Software Foundation

Apache Commons Codec
Copyright 2002-2020 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (https://www.apache.org/).

src/test/org/apache/commons/codec/language/DoubleMetaphoneTest.java
contains test data from http://aspell.net/test/orig/batch0.tab.
Copyright (C) 2002 Kevin Atkinson (<EMAIL>)

===============================================================================

The content of package org.apache.commons.codec.language.bm has been translated
from the original php source code available at http://stevemorse.org/phoneticinfo.htm
with permission from the original authors.
Original source copyright:
Copyright (c) 2008 Alexander Beider & Stephen P. Morse.

Apache Commons IO
Copyright 2002-2021 The Apache Software Foundation

// ------------------------------------------------------------------
// NOTICE file corresponding to the section 4d of The Apache License,
// Version 2.0, in this case for Apache Flink
// ------------------------------------------------------------------

Apache Flink
Copyright 2006-2023 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

flink-table-runtime
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the Apache Software License 2.0. (http://www.apache.org/licenses/LICENSE-2.0.txt)

- com.jayway.jsonpath:json-path:2.6.0
- org.codehaus.janino:janino:3.0.11
- org.codehaus.janino:commons-compiler:3.0.11


flink-table-code-splitter
Copyright 2014-2022 The Apache Software Foundation

This project bundles the following dependencies under the BSD 3-clause license.
See bundled license files for details.

- org.antlr:antlr4-runtime:4.7

This project bundles the following files under the BSD license.
See bundled license files for details.

- Antlr Java grammar files (https://github.com/antlr/grammars-v4/tree/master/java/java)
  -> in src/main/antlr4


Apache Log4j 1.x Compatibility API
Copyright 1999-1969 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).



Apache Log4j API
Copyright 1999-1969 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


Apache Log4j Core
Copyright 1999-2012 Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

ResolverUtil.java
Copyright 2005-2006 Tim Fennell
Apache Log4j SLF4J Binding
Copyright 1999-1969 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).


