#!/usr/bin/env python3
"""
Real-time monitoring script for Flink CEP filtering efficiency
Tracks the 85%+ benign transaction filtering target
"""

import asyncio
import json
import time
import logging
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import aiohttp
from confluent_kafka import Consumer, KafkaException

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class FilteringMetrics:
    """Metrics for tracking CEP filtering efficiency"""
    timestamp: str
    input_transactions: int
    filtered_transactions: int
    filtering_ratio: float
    tier1_filtered: int  # Statistical filter
    tier2_filtered: int  # SQL pattern filter  
    tier3_filtered: int  # CEP pattern filter
    suspicious_alerts: int
    processing_latency_ms: float
    throughput_tx_per_sec: float

@dataclass 
class AlertMetrics:
    """Metrics for suspicious transaction alerts"""
    alert_type: str
    count: int
    confidence_avg: float
    addresses_flagged: int

class CEPFilteringMonitor:
    """Monitor for CEP filtering performance and efficiency"""
    
    def __init__(self):
        self.kafka_bootstrap = "localhost:9092"  # Update for your setup
        self.flink_rest_api = "http://localhost:8081"
        
        # Tracking variables
        self.metrics_history: List[FilteringMetrics] = []
        self.alert_metrics: Dict[str, AlertMetrics] = {}
        
        # Performance targets
        self.target_filtering_ratio = 0.85  # 85% target
        self.target_throughput = 1000  # tx/s
        self.target_latency_ms = 200   # ms
        
        logger.info("CEP Filtering Monitor initialized")
        
    async def monitor_kafka_topics(self):
        """Monitor Kafka topics for input/output transaction counts"""
        
        # Configure consumers for different topics
        consumer_config = {
            'bootstrap.servers': self.kafka_bootstrap,
            'group.id': 'cep-monitor',
            'auto.offset.reset': 'latest',
            'enable.auto.commit': True
        }
        
        input_consumer = Consumer({**consumer_config, 'group.id': 'cep-monitor-input'})
        output_consumer = Consumer({**consumer_config, 'group.id': 'cep-monitor-output'})
        alerts_consumer = Consumer({**consumer_config, 'group.id': 'cep-monitor-alerts'})
        
        # Subscribe to topics
        input_consumer.subscribe(['ethereum-transactions'])
        output_consumer.subscribe(['filtered-transactions'])  
        alerts_consumer.subscribe(['suspicious-alerts'])
        
        logger.info("Started monitoring Kafka topics...")
        
        # Metrics tracking
        input_count = 0
        output_count = 0
        alerts_count = 0
        start_time = time.time()
        
        try:
            while True:
                # Poll each consumer
                input_msgs = input_consumer.consume(timeout=1.0, num_messages=100)
                output_msgs = output_consumer.consume(timeout=1.0, num_messages=100)  
                alert_msgs = alerts_consumer.consume(timeout=1.0, num_messages=10)
                
                # Count messages
                input_count += len(input_msgs)
                output_count += len(output_msgs)
                alerts_count += len(alert_msgs)
                
                # Process alert messages for detailed metrics
                for msg in alert_msgs:
                    if msg.error():
                        continue
                    self._process_alert_message(msg.value().decode('utf-8'))
                
                # Calculate metrics every 30 seconds
                elapsed = time.time() - start_time
                if elapsed >= 30:  # Every 30 seconds
                    await self._calculate_and_log_metrics(
                        input_count, output_count, alerts_count, elapsed
                    )
                    
                    # Reset counters
                    input_count = output_count = alerts_count = 0
                    start_time = time.time()
                
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
        finally:
            input_consumer.close()
            output_consumer.close()
            alerts_consumer.close()
    
    def _process_alert_message(self, alert_json: str):
        """Process individual alert messages for detailed metrics"""
        try:
            alert = json.loads(alert_json)
            alert_type = alert.get('pattern_type', 'UNKNOWN')
            confidence = alert.get('confidence_score', 0.0)
            
            if alert_type not in self.alert_metrics:
                self.alert_metrics[alert_type] = AlertMetrics(
                    alert_type=alert_type,
                    count=0,
                    confidence_avg=0.0,
                    addresses_flagged=0
                )
            
            # Update metrics
            metrics = self.alert_metrics[alert_type]
            metrics.count += 1
            
            # Running average of confidence
            metrics.confidence_avg = (
                (metrics.confidence_avg * (metrics.count - 1) + confidence) / metrics.count
            )
            
        except json.JSONDecodeError:
            logger.warning(f"Invalid alert JSON: {alert_json}")
    
    async def _calculate_and_log_metrics(self, input_count: int, output_count: int, 
                                       alerts_count: int, elapsed_time: float):
        """Calculate and log filtering efficiency metrics"""
        
        # Calculate filtering ratio
        if input_count > 0:
            filtering_ratio = 1.0 - (output_count / input_count)
            filtered_count = input_count - output_count
        else:
            filtering_ratio = 0.0
            filtered_count = 0
        
        # Calculate throughput
        throughput = input_count / elapsed_time if elapsed_time > 0 else 0
        
        # Get Flink metrics for latency
        flink_latency = await self._get_flink_latency_metrics()
        
        # Create metrics object
        metrics = FilteringMetrics(
            timestamp=datetime.now().isoformat(),
            input_transactions=input_count,
            filtered_transactions=filtered_count,
            filtering_ratio=filtering_ratio,
            tier1_filtered=int(filtered_count * 0.65),  # Estimated distribution
            tier2_filtered=int(filtered_count * 0.20),
            tier3_filtered=int(filtered_count * 0.15),
            suspicious_alerts=alerts_count,
            processing_latency_ms=flink_latency,
            throughput_tx_per_sec=throughput
        )
        
        # Store metrics
        self.metrics_history.append(metrics)
        
        # Keep only last 100 metrics entries
        if len(self.metrics_history) > 100:
            self.metrics_history = self.metrics_history[-100:]
        
        # Log current metrics
        self._log_metrics(metrics)
        
        # Check if targets are being met
        self._check_performance_targets(metrics)
    
    async def _get_flink_latency_metrics(self) -> float:
        """Fetch latency metrics from Flink REST API"""
        try:
            async with aiohttp.ClientSession() as session:
                # Get job overview
                async with session.get(f"{self.flink_rest_api}/jobs/overview") as resp:
                    if resp.status == 200:
                        jobs = await resp.json()
                        if jobs.get('jobs'):
                            job_id = jobs['jobs'][0]['jid']
                            
                            # Get job metrics
                            async with session.get(
                                f"{self.flink_rest_api}/jobs/{job_id}/metrics"
                            ) as metrics_resp:
                                if metrics_resp.status == 200:
                                    metrics_data = await metrics_resp.json()
                                    # Extract latency metric (simplified)
                                    return 150.0  # Placeholder - would parse actual metrics
                                    
        except Exception as e:
            logger.warning(f"Could not fetch Flink metrics: {e}")
        
        return 0.0
    
    def _log_metrics(self, metrics: FilteringMetrics):
        """Log current filtering metrics"""
        
        # Status indicators
        ratio_status = "✅" if metrics.filtering_ratio >= self.target_filtering_ratio else "⚠️"
        throughput_status = "✅" if metrics.throughput_tx_per_sec >= self.target_throughput else "⚠️"
        latency_status = "✅" if metrics.processing_latency_ms <= self.target_latency_ms else "⚠️"
        
        logger.info("=" * 80)
        logger.info("🔍 CEP FILTERING PERFORMANCE METRICS")
        logger.info("=" * 80)
        logger.info(f"📊 Filtering Efficiency: {ratio_status}")
        logger.info(f"   • Input Transactions: {metrics.input_transactions:,}")
        logger.info(f"   • Filtered Out: {metrics.filtered_transactions:,}")
        logger.info(f"   • Filtering Ratio: {metrics.filtering_ratio:.1%} (Target: {self.target_filtering_ratio:.1%})")
        logger.info("")
        logger.info(f"⚡ Performance Metrics: {throughput_status} {latency_status}")
        logger.info(f"   • Throughput: {metrics.throughput_tx_per_sec:.1f} tx/s (Target: {self.target_throughput})")
        logger.info(f"   • Latency: {metrics.processing_latency_ms:.1f}ms (Target: <{self.target_latency_ms}ms)")
        logger.info("")
        logger.info(f"🚨 Suspicious Alerts: {metrics.suspicious_alerts}")
        logger.info("")
        logger.info(f"🥅 Filter Breakdown (Estimated):")
        logger.info(f"   • Tier 1 (Statistical): {metrics.tier1_filtered:,} transactions")
        logger.info(f"   • Tier 2 (SQL Patterns): {metrics.tier2_filtered:,} transactions")  
        logger.info(f"   • Tier 3 (CEP Patterns): {metrics.tier3_filtered:,} transactions")
        
        # Alert type breakdown
        if self.alert_metrics:
            logger.info("")
            logger.info("🔔 Alert Type Breakdown:")
            for alert_type, alert_data in self.alert_metrics.items():
                logger.info(f"   • {alert_type}: {alert_data.count} alerts "
                           f"(avg confidence: {alert_data.confidence_avg:.2f})")
        
        logger.info("=" * 80)
    
    def _check_performance_targets(self, metrics: FilteringMetrics):
        """Check if performance targets are being met and log warnings"""
        
        warnings = []
        
        # Check filtering ratio
        if metrics.filtering_ratio < self.target_filtering_ratio:
            shortfall = self.target_filtering_ratio - metrics.filtering_ratio
            warnings.append(f"Filtering ratio {metrics.filtering_ratio:.1%} is "
                          f"{shortfall:.1%} below target {self.target_filtering_ratio:.1%}")
        
        # Check throughput
        if metrics.throughput_tx_per_sec < self.target_throughput:
            shortfall = self.target_throughput - metrics.throughput_tx_per_sec
            warnings.append(f"Throughput {metrics.throughput_tx_per_sec:.1f} tx/s is "
                          f"{shortfall:.1f} tx/s below target {self.target_throughput}")
        
        # Check latency
        if metrics.processing_latency_ms > self.target_latency_ms:
            excess = metrics.processing_latency_ms - self.target_latency_ms
            warnings.append(f"Latency {metrics.processing_latency_ms:.1f}ms is "
                          f"{excess:.1f}ms above target {self.target_latency_ms}ms")
        
        # Log warnings
        if warnings:
            logger.warning("⚠️  PERFORMANCE WARNINGS:")
            for warning in warnings:
                logger.warning(f"   • {warning}")
            logger.warning("   Consider adjusting CEP parameters or scaling resources")
    
    async def generate_performance_report(self) -> Dict:
        """Generate comprehensive performance report"""
        
        if not self.metrics_history:
            return {"error": "No metrics data available"}
        
        recent_metrics = self.metrics_history[-10:]  # Last 10 measurements
        
        # Calculate averages
        avg_filtering_ratio = sum(m.filtering_ratio for m in recent_metrics) / len(recent_metrics)
        avg_throughput = sum(m.throughput_tx_per_sec for m in recent_metrics) / len(recent_metrics)
        avg_latency = sum(m.processing_latency_ms for m in recent_metrics) / len(recent_metrics)
        
        # Calculate trends
        if len(recent_metrics) >= 2:
            ratio_trend = recent_metrics[-1].filtering_ratio - recent_metrics[0].filtering_ratio
            throughput_trend = recent_metrics[-1].throughput_tx_per_sec - recent_metrics[0].throughput_tx_per_sec
        else:
            ratio_trend = throughput_trend = 0.0
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "target_filtering_ratio": self.target_filtering_ratio,
                "current_filtering_ratio": avg_filtering_ratio,
                "target_met": avg_filtering_ratio >= self.target_filtering_ratio,
                "filtering_trend": "increasing" if ratio_trend > 0 else "decreasing" if ratio_trend < 0 else "stable"
            },
            "performance": {
                "avg_throughput_tx_per_sec": avg_throughput,
                "avg_latency_ms": avg_latency,
                "throughput_trend": "increasing" if throughput_trend > 0 else "decreasing" if throughput_trend < 0 else "stable"
            },
            "alert_summary": dict(self.alert_metrics),
            "recommendations": self._generate_recommendations(avg_filtering_ratio, avg_throughput, avg_latency)
        }
        
        return report
    
    def _generate_recommendations(self, filtering_ratio: float, throughput: float, latency: float) -> List[str]:
        """Generate performance improvement recommendations"""
        
        recommendations = []
        
        if filtering_ratio < self.target_filtering_ratio:
            recommendations.append("Increase filtering aggressiveness by lowering thresholds")
            recommendations.append("Review and expand benign transaction patterns")
        
        if throughput < self.target_throughput:
            recommendations.append("Scale up Flink TaskManager instances")
            recommendations.append("Increase Kafka partitions for better parallelism")
            
        if latency > self.target_latency_ms:
            recommendations.append("Optimize CEP pattern complexity")
            recommendations.append("Increase Flink memory allocation")
        
        if not recommendations:
            recommendations.append("System is performing within target parameters")
            
        return recommendations

async def main():
    """Main monitoring loop"""
    monitor = CEPFilteringMonitor()
    
    try:
        # Start monitoring
        await monitor.monitor_kafka_topics()
        
    except KeyboardInterrupt:
        logger.info("Monitoring stopped")
        
        # Generate final report
        report = await monitor.generate_performance_report()
        
        logger.info("📋 FINAL PERFORMANCE REPORT:")
        logger.info(json.dumps(report, indent=2))

if __name__ == "__main__":
    asyncio.run(main())