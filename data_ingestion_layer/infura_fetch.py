import asyncio
from web3 import Web3
from web3.middleware import geth_poa_middleware

INFURA_URL = "https://sepolia.infura.io/v3/********************************"
w3 = Web3(Web3.HTTPProvider(INFURA_URL))
w3.middleware_onion.inject(geth_poa_middleware, layer = 0)


async def fetch_latest_block():
    while True:
        block = w3.eth.get_block('latest' full_transaction=True)
        print("Block", block.number, "with", len(block.transactions), "txs")
        await asyncio.sleep(5)

async.run(fetch_latest_block())