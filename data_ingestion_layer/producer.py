from confluent_kafka import Producer
import json, time

p = Producer({'bootstrap.servers':'localhost:9092',
            'enable.idempotence': True})

def delivery_report(err, msg):
    if err: print('Delivery failed:', err)
    else: print('Delivered to', msg.topic(), msg.partition())

for i in range(5):
    record = {'txID': f'tx{i}', 'valueETH': i*0.1, 'from': '0xFoo', 'to': '0xBar'}
    p.produce('raw-transactions', json.dumps(record), callback=delivery_report)
    p.pull(0)
    time.sleep(1)
p.flush()