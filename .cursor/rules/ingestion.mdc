---
description: 
globs: "ingestion/**"
alwaysApply: false
---
---
# Ingestion Layer: ensure user consent before code generation
description: |
  Before generating any code for the ingestion layer (Kafka + Infura), the agent must ask for user permission.
globs:
  - "ingestion/**"
alwaysApply: true
prePrompt: "⚙️ This will generate code for the **Ingestion Layer** (Kafka consumer & Infura). May I proceed?"
postPrompt: "✅ Ingestion code generated. Does the module follow connection abstraction and config-driven patterns?"
denyPatterns:
  - delete
  - replace
description: |
  **Ingestion Layer** must:
  - Use Kafka producer/consumer with configurable brokers and topics.
  - Connect to Infura via a reusable WebSocket module.
  - Include header comments and function docstrings for maintainability.
  - Provide at least one unit-test stub in `tests/ingestion_test.py`.
  - Pass linting (`npm run lint` or `flake8`) before commit.
---