# 🛡️ Real-Time On-Chain Anomaly Detection System (SRD)
### 🇲🇾 For Malaysia's Central Bank Digital Currency (CBDC)
#### Powered by Big Data & Blockchain Technologies for Anti-Money Laundering (AML)

---

## 📘 1. Project Overview

**Project Title:**  
> Real-Time On-Chain Anomaly Detection in Malaysia’s Central Bank Digital Currency System for AML

**Purpose:**  
> To develop a real-time, AI-powered system that detects suspicious financial transactions on the CBDC blockchain, flags anomalies using machine learning, and supports anti-money laundering enforcement as per Bank Negara Malaysia’s guidelines.

**Scope:**  
> This system will ingest CBDC blockchain transactions from `opencbdc-tx`, analyze them with ML models, raise alerts on anomalies, allow AML officers to investigate, and generate reports for regulatory bodies.

---

## 🔍 2. Use Cases

| Use Case ID | Description                                      | Actor         |
|-------------|--------------------------------------------------|---------------|
| UC01        | Monitor live blockchain transactions             | AML Officer   |
| UC02        | Detect anomalous behavior in real-time           | AI Model      |
| UC03        | Trigger alerts when anomalies are detected       | Alert Engine  |
| UC04        | Investigate flagged transactions via dashboard   | Compliance    |
| UC05        | Generate Suspicious Transaction Reports (STR)    | Analyst       |
| UC06        | Auto-block flagged wallets via smart contracts   | Smart Contract|
| UC07        | Log and analyze UTXO metadata from opencbdc-tx   | Data Engineer |

---

## 🏗️ 3. System Architecture Summary

**Major Components:**
- ✅ Frontend Dashboard (React.js)
- ✅ API Gateway (Kong / NGINX)
- ✅ Microservices (Node.js / Flask)
- ✅ Kafka + Flink (Real-time processing)
- ✅ TensorFlow / PyTorch (AI/ML)
- ✅ Blockchain (Hyperledger / Quorum)
- ✅ PostgreSQL + MongoDB
- ✅ HDFS (Historical data)
- ✅ Security Stack (OAuth, TLS, Audit logs)
- ✅ CBDC Transaction Simulator (`opencbdc-tx`)

---

## ⚙️ 4. Functional Requirements

| ID    | Requirement                                                                 |
|-------|------------------------------------------------------------------------------|
| FR01  | The system must capture and stream real-time transactions from the blockchain. |
| FR02  | The system must score transactions for anomalies using ML models.           |
| FR03  | The system must store flagged transactions in a database.                   |
| FR04  | The system must generate alerts for suspicious transactions.                |
| FR05  | Users must be able to view and manage alerts via a dashboard.               |
| FR06  | The system must allow creating and closing investigation cases.             |
| FR07  | The system must generate and export STR reports.                            |
| FR08  | Smart contracts must block/flag confirmed high-risk addresses.              |
| FR09  | The system must integrate with `opencbdc-tx` to simulate real CBDC transactions. |

---

## 🔐 5. Non-Functional Requirements

| ID    | Requirement                                        |
|-------|----------------------------------------------------|
| NFR01 | The system must process transactions with <1s latency. |
| NFR02 | All sensitive data must be encrypted at rest and in transit. |
| NFR03 | The system must be highly available (≥ 99.9%).     |
| NFR04 | Audit logs must be stored and immutable.           |
| NFR05 | Compliance reports must be exportable in PDF/JSON. |

---

## 🗃️ 6. Database & Schema Summary

- **PostgreSQL**:
  - Users, Alerts, Cases, Compliance Reports
- **MongoDB / Cassandra**:
  - Anomaly metadata, raw transaction info
- **HDFS**:
  - Archived transactions for model retraining

---

## 🧠 7. AI/ML Pipeline Requirements

| ID    | Requirement                                         |
|-------|-----------------------------------------------------|
| ML01  | The system must use at least one unsupervised anomaly detection model. |
| ML02  | Models must be versioned and deployed via an API.   |
| ML03  | Transaction graphs should be analyzed using GNNs.   |
| ML04  | ML pipeline must support continuous learning.        |

---

## 🌐 8. Blockchain Requirements

| ID    | Requirement                                                  |
|-------|--------------------------------------------------------------|
| BC01  | Transactions must be sourced from a permissioned blockchain. |
| BC02  | AML rules must be enforced using smart contracts.            |
| BC03  | The system must integrate with blockchain oracles (KYC/Sanctions). |

---

## 🚀 9. MVP Milestones

| Phase     | Deliverables                                  | Time Estimate |
|-----------|-----------------------------------------------|---------------|
| Phase 0   | Integrate `opencbdc-tx` + stream output to Kafka | Week 0–1      |
| Phase 1   | Basic Kafka stream + dummy ML detection        | Week 1–2      |
| Phase 2   | Alerts system + PostgreSQL storage             | Week 3–4      |
| Phase 3   | AML dashboard (alerts + investigation)         | Week 5–6      |
| Phase 4   | Smart contract enforcement + STR export        | Week 7–8      |

---

## 🧑‍💻 10. Team Roles

| Role                  | Responsibility                                     |
|-----------------------|---------------------------------------------------|
| Project Manager       | Oversee scope, milestones, and team coordination  |
| Backend Developer     | Build APIs, microservices, and database layer     |
| Data Engineer         | Manage Kafka, Flink, and transaction ingestion    |
| ML Engineer           | Train and deploy anomaly detection models         |
| Blockchain Developer  | Deploy and test smart contracts                   |
| Frontend Developer    | Build React dashboard and user interface          |
| Compliance Analyst    | Define AML rules and STR workflows                |

## 🛠️ 11. Architecture Improvement Recommendations

To ensure the system remains robust, scalable, and secure as CBDC adoption expands, the following technical improvements are proposed:

1. **Frontend Enhancements**: RBAC, real-time geospatial heatmaps, and team collaboration features.
2. **Microservice Communication**: Introduce gRPC and service registry for scalability.
3. **ML Pipeline Improvements**: Integrate SHAP/LIME for model interpretability, and continuous learning capabilities.
4. **Security Expansion**: WAF, compliance automation, tokenization, and smart contract metrics.
5. **Blockchain Visibility**: Add multi-chain monitors and privacy-preserving mechanisms like ZKP.
6. **Infra Resilience**: Automate backups, adopt Elasticsearch and TimescaleDB, and include fraud simulation environments.

## 🧠 Innovation Justification

This project aims to surpass existing anti-money laundering (AML) systems by addressing their limitations and introducing innovations particularly suited for real-time, blockchain-based financial systems like Malaysia’s Central Bank Digital Currency (CBDC). While current industry-grade systems (e.g., Elliptic, Chainalysis, HSBC, ING) leverage strong machine learning and network analysis methods, many operate in near-real-time or batch-processing environments and are limited by black-box models and lack of temporal responsiveness. This project seeks to go beyond these limitations in several key ways:

---

### 🚀 1. **True Real-Time Detection with Minimal Latency**
Most enterprise AML systems operate on lagged data or batch intervals. This project differentiates itself by building a **real-time streaming architecture** using:

- **Kafka + Apache Flink**: For continuous ingestion and event-driven processing of CBDC transactions.
- **FastAPI + ONNX**: For low-latency ML inference with pre-loaded models.
- **Model latency target**: <500 milliseconds end-to-end processing per transaction.

> 🔍 *Why it matters:* CBDC environments demand instant fraud detection to freeze funds, alert investigators, and comply with real-time digital currency frameworks.

---

### 🧩 2. **Multi-Layer Hybrid Detection Pipeline**
Rather than relying on a single model, the system uses a **three-layer architecture**:

1. **Feature-Based Layer**: Fast filters using Isolation Forest / Autoencoders.
2. **Graph-Based Layer**: Wallet-to-wallet behavior analysis via GraphSAGE or GAT.
3. **Temporal Layer**: Time-sequenced anomaly patterns with TGAT or DyGNN.

This modular pipeline mimics real-world intelligence filtering, allowing detection of behavioral, structural, and temporal laundering patterns.

---

### 🔍 3. **Explainability-Driven AML**
While black-box models are common in AML, they hinder trust and adoption.

- **SHAP/LIME**: For interpretable scoring in Isolation Forest and Autoencoders.
- **Attention Maps**: For understanding GNN decisions.
- **Narrative Outputs**: Natural-language rule synthesis to aid compliance teams.

This ensures analysts can understand *why* a transaction is flagged, satisfying regulatory transparency.

---

### 🔄 4. **Online Learning & Drift Adaptation**
Static models decay quickly in adversarial domains. This system incorporates:

- **Drift detection** using model confidence entropy.
- **Human-in-the-loop feedback loops** for retraining.
- **Federated Learning Readiness** for future multi-institution AML collaboration.

---

### 🌐 5. **Future-Ready: Cross-Chain and Multi-Modal AML**
Unlike current systems restricted to a single blockchain or account structure, this system is designed to:

- Simulate **cross-chain laundering** (e.g., Fabric → Quorum wallet trail).
- Incorporate **biometric and device metadata** (for fusion AML/fraud systems).

---

### ✅ Summary
This project is not just an AML detector — it is an extensible, real-time intelligence engine for blockchain-based financial ecosystems. With minimal latency, hybrid modeling, explainability, and future-proof architecture, it exceeds the capability of many current production systems in scalability, responsiveness, and regulatory alignment.

> "Where most systems stop at batch scoring, this project enables live digital forensics at the speed of the CBDC ledger."